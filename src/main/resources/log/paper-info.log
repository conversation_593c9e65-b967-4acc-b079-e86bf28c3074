2025-07-27 14:28:37.063 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 14:28:37.281 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 14:28:37.281 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 14:28:37.323 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 14:46:05.500 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 14:46:05.587 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 14:46:05.587 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 14:46:05.614 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 14:53:30.129 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 14:53:30.231 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 14:53:30.231 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 14:53:30.251 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 15:07:34.101 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 15:07:34.260 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 15:07:34.260 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 15:07:34.291 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 15:12:23.892 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 15:12:23.999 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 15:12:23.999 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 15:12:24.022 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 15:15:07.987 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 15:15:08.078 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=41, pages=9, current=1, size=5, records=5
2025-07-27 15:15:45.011 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 开始批量生成试卷: 标题='测试试卷', 套数=3
2025-07-27 15:15:45.011 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='测试试卷 (第1套)', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={medium=0.5, hard=0.2, easy=0.3}
2025-07-27 15:15:45.012 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(paperId=null, knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], title=测试试卷 (第1套), totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={medium=0.5, hard=0.2, easy=0.3}, topicTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-07-27 15:15:45.015 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Converted type counts for entire process: frontend={SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0} -> database={multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:15:45.046 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 602 contributed 361 questions
2025-07-27 15:15:45.071 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 603 contributed 434 questions
2025-07-27 15:15:45.114 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 269 contributed 821 questions
2025-07-27 15:15:45.155 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 270 contributed 806 questions
2025-07-27 15:15:45.161 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 当前题目池已满足所有题型要求，无需扩展
2025-07-27 15:15:45.161 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final topic pool size after potential expansion: 2422. Target global counts: {SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:15:45.190 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 2422 topics. IDs: [384948, 384949, 384950, 384951, 384952, 384953, 384954, 384955, 384956, 384957, 384958, 384959, 384960, 384961, 384962, 384963, 384964, 384965, 384966, 384967, 384968, 384969, 384970, 384971, 384972, 384973, 384974, 384975, 384976, 384977, 384978, 384979, 384980, 384981, 384982, 384983, 384984, 384985, 384986, 384987, 384988, 384989, 384990, 384991, 384992, 384993, 384994, 384995, 384996, 384997, 384998, 384999, 385000, 385001, 385002, 385003, 385004, 385005, 385006, 385007, 385008, 385009, 385010, 385011, 385012, 385013, 385014, 385015, 385016, 385017, 385018, 385019, 385020, 385021, 385022, 385023, 385024, 385025, 385026, 385027, 385028, 385029, 385030, 385031, 385032, 385033, 385034, 385035, 385036, 385037, 385038, 385039, 385040, 385041, 385042, 385043, 385044, 385045, 385046, 385047, 385048, 385049, 385050, 385051, 385052, 385053, 385054, 385055, 385056, 385057, 385058, 385059, 385060, 385061, 385062, 385063, 385064, 385065, 385066, 385067, 385068, 385069, 385070, 385071, 385072, 385073, 385074, 385075, 385076, 385077, 385078, 385079, 385080, 385081, 385082, 385083, 385084, 385085, 385086, 385087, 385088, 385089, 385090, 385091, 385092, 385093, 385094, 385095, 385096, 385097, 385098, 385099, 385100, 385101, 385102, 385103, 385104, 385105, 385106, 385107, 385108, 385109, 385110, 385111, 385112, 385113, 385114, 385115, 385116, 385117, 385118, 385119, 385120, 385121, 385122, 385123, 385124, 385125, 385126, 385127, 385128, 385129, 385130, 385131, 385132, 385133, 385134, 385135, 385136, 385137, 385138, 385139, 385140, 385141, 385142, 385143, 385144, 385145, 385146, 385147, 385148, 385149, 385150, 385151, 385152, 385153, 385154, 385155, 385156, 385157, 385158, 385159, 385160, 385161, 385162, 385163, 385164, 385165, 385166, 385167, 385168, 385169, 385170, 385171, 385172, 385173, 385174, 385175, 385176, 385177, 385178, 385179, 385180, 385181, 385182, 385183, 385184, 385185, 385186, 385187, 385188, 385189, 385190, 385191, 385192, 385193, 385194, 385195, 385196, 385197, 385198, 385199, 385200, 385201, 385202, 385203, 385204, 385205, 385206, 385207, 385208, 385209, 385210, 385211, 385212, 385213, 385214, 385215, 385216, 385217, 385218, 385219, 385220, 385221, 385222, 385223, 385224, 385225, 385226, 385227, 385228, 385229, 385230, 385231, 385232, 385233, 385234, 385235, 385236, 385237, 385238, 385239, 385240, 385241, 385242, 385243, 385244, 385245, 385246, 385247, 385248, 385249, 385250, 385251, 385252, 385253, 385254, 385255, 385256, 385257, 385258, 385259, 385260, 385261, 385262, 385263, 385264, 385265, 385266, 385267, 385268, 385269, 385270, 385271, 385272, 385273, 385274, 385275, 385276, 385277, 385278, 385279, 385280, 385281, 385282, 385283, 385284, 385285, 385286, 385287, 385288, 385289, 385290, 385291, 385292, 385293, 385294, 385295, 385296, 385297, 385298, 385299, 385300, 385301, 385302, 385303, 385304, 385305, 385306, 385307, 385308, 384514, 384515, 384516, 384517, 384518, 384519, 384520, 384521, 384522, 384523, 384524, 384525, 384526, 384527, 384528, 384529, 384530, 384531, 384532, 384533, 384534, 384535, 384536, 384537, 384538, 384539, 384540, 384541, 384542, 384543, 384544, 384545, 384546, 384547, 384548, 384549, 384550, 384551, 384552, 384553, 384554, 384555, 384556, 384557, 384558, 384559, 384560, 384561, 384562, 384563, 384564, 384565, 384566, 384567, 384568, 384569, 384570, 384571, 384572, 384573, 384574, 384575, 384576, 384577, 384578, 384579, 384580, 384581, 384582, 384583, 384584, 384585, 384586, 384587, 384588, 384589, 384590, 384591, 384592, 384593, 384594, 384595, 384596, 384597, 384598, 384599, 384600, 384601, 384602, 384603, 384604, 384605, 384606, 384607, 384608, 384609, 384610, 384611, 384612, 384613, 384614, 384615, 384616, 384617, 384618, 384619, 384620, 384621, 384622, 384623, 384624, 384625, 384626, 384627, 384628, 384629, 384630, 384631, 384632, 384633, 384634, 384635, 384636, 384637, 384638, 384639, 384640, 384641, 384642, 384643, 384644, 384645, 384646, 384647, 384648, 384649, 384650, 384651, 384652, 384653, 384654, 384655, 384656, 384657, 384658, 384659, 384660, 384661, 384662, 384663, 384664, 384665, 384666, 384667, 384668, 384669, 384670, 384671, 384672, 384673, 384674, 384675, 384676, 384677, 384678, 384679, 384680, 384681, 384682, 384683, 384684, 384685, 384686, 384687, 384688, 384689, 384690, 384691, 384692, 384693, 384694, 384695, 384696, 384697, 384698, 384699, 384700, 384701, 384702, 384703, 384704, 384705, 384706, 384707, 384708, 384709, 384710, 384711, 384712, 384713, 384714, 384715, 384716, 384717, 384718, 384719, 384720, 384721, 384722, 384723, 384724, 384725, 384726, 384727, 384728, 384729, 384730, 384731, 384732, 384733, 384734, 384735, 384736, 384737, 384738, 384739, 384740, 384741, 384742, 384743, 384744, 384745, 384746, 384747, 384748, 384749, 384750, 384751, 384752, 384753, 384754, 384755, 384756, 384757, 384758, 384759, 384760, 384761, 384762, 384763, 384764, 384765, 384766, 384767, 384768, 384769, 384770, 384771, 384772, 384773, 384774, 384775, 384776, 384777, 384778, 384779, 384780, 384781, 384782, 384783, 384784, 384785, 384786, 384787, 384788, 384789, 384790, 384791, 384792, 384793, 384794, 384795, 384796, 384797, 384798, 384799, 384800, 384801, 384802, 384803, 384804, 384805, 384806, 384807, 384808, 384809, 384810, 384811, 384812, 384813, 384814, 384815, 384816, 384817, 384818, 384819, 384820, 384821, 384822, 384823, 384824, 384825, 384826, 384827, 384828, 384829, 384830, 384831, 384832, 384833, 384834, 384835, 384836, 384837, 384838, 384839, 384840, 384841, 384842, 384843, 384844, 384845, 384846, 384847, 384848, 384849, 384850, 384851, 384852, 384853, 384854, 384855, 384856, 384857, 384858, 384859, 384860, 384861, 384862, 384863, 384864, 384865, 384866, 384867, 384868, 384869, 384870, 384871, 384872, 384873, 384874, 384875, 384876, 384877, 384878, 384879, 384880, 384881, 384882, 384883, 384884, 384885, 384886, 384887, 384888, 384889, 384890, 384891, 384892, 384893, 384894, 384895, 384896, 384897, 384898, 384899, 384900, 384901, 384902, 384903, 384904, 384905, 384906, 384907, 384908, 384909, 384910, 384911, 384912, 384913, 384914, 384915, 384916, 384917, 384918, 384919, 384920, 384921, 384922, 384923, 384924, 384925, 384926, 384927, 384928, 384929, 384930, 384931, 384932, 384933, 384934, 384935, 384936, 384937, 384938, 384939, 384940, 384941, 384942, 384943, 384944, 384945, 384946, 384947, 331479, 331480, 331481, 331482, 331483, 331484, 331485, 331486, 331487, 331488, 331489, 331490, 331491, 331492, 331493, 331494, 331495, 331496, 331497, 331498, 331499, 331500, 331501, 331502, 331503, 331504, 331505, 331506, 331507, 331508, 331509, 331510, 331511, 331512, 331513, 331514, 331515, 331516, 331517, 331518, 331519, 331520, 331521, 331522, 331523, 331524, 331525, 331526, 331527, 331528, 331529, 331530, 331531, 331532, 331533, 331534, 331535, 331536, 331537, 331538, 331539, 331540, 331541, 331542, 331543, 331544, 331545, 331546, 331547, 331548, 331549, 331550, 331551, 331552, 331553, 331554, 331555, 331556, 331557, 331558, 331559, 331560, 331561, 331562, 331563, 331564, 331565, 331566, 331567, 331568, 331569, 331570, 331571, 331572, 331573, 331574, 331575, 331576, 331577, 331578, 331579, 331580, 331581, 331582, 331583, 331584, 331585, 331586, 331587, 331588, 331589, 331590, 331591, 331592, 331593, 331594, 331595, 331596, 331597, 331598, 331599, 331600, 331601, 331602, 331603, 331604, 331605, 331606, 331607, 331608, 331609, 331610, 331611, 331612, 331613, 331614, 331615, 331616, 331617, 331618, 331619, 331620, 331621, 331622, 331623, 331624, 331625, 331626, 331627, 331628, 331629, 331630, 331631, 331632, 331633, 331634, 331635, 331636, 331637, 331638, 331639, 331640, 331641, 331642, 331643, 331644, 331645, 331646, 331647, 331648, 331649, 331650, 331651, 331652, 331653, 331654, 331655, 331656, 331657, 331658, 331659, 331660, 331661, 331662, 331663, 331664, 331665, 331666, 331667, 331668, 331669, 331670, 331671, 331672, 331673, 331674, 331675, 331676, 331677, 331678, 331679, 331680, 331681, 331682, 331683, 331684, 331685, 331686, 331687, 331688, 331689, 331690, 331691, 331692, 331693, 331694, 331695, 331696, 331697, 331698, 331699, 331700, 331701, 331702, 331703, 331704, 331705, 331706, 331707, 331708, 331709, 331710, 331711, 331712, 331713, 331714, 331715, 331716, 331717, 331718, 331719, 331720, 331721, 331722, 331723, 331724, 331725, 331726, 331727, 331728, 331729, 331730, 331731, 331732, 331733, 331734, 331735, 331736, 331737, 331738, 331739, 331740, 331741, 331742, 331743, 331744, 331745, 331746, 331747, 331748, 331749, 331750, 331751, 331752, 331753, 331754, 331755, 331756, 331757, 331758, 331759, 331760, 331761, 331762, 331763, 331764, 331765, 331766, 331767, 331768, 331769, 331770, 331771, 331772, 331773, 331774, 331775, 331776, 331777, 331778, 331779, 331780, 331781, 331782, 331783, 331784, 331785, 331786, 331787, 331788, 331789, 331790, 331791, 331792, 331793, 331794, 331795, 331796, 331797, 331798, 331799, 331800, 331801, 331802, 331803, 331804, 331805, 331806, 331807, 331808, 331809, 331810, 331811, 331812, 331813, 331814, 331815, 331816, 331817, 331818, 331819, 331820, 331821, 331822, 331823, 331824, 331825, 331826, 331827, 331828, 331829, 331830, 331831, 331832, 331833, 331834, 331835, 331836, 331837, 331838, 331839, 331840, 331841, 331842, 331843, 331844, 331845, 331846, 331847, 331848, 331849, 331850, 331851, 331852, 331853, 331854, 331855, 331856, 331857, 331858, 331859, 331860, 331861, 331862, 331863, 331864, 331865, 331866, 331867, 331868, 331869, 331870, 331871, 331872, 331873, 331874, 331875, 331876, 331877, 331878, 331879, 331880, 331881, 331882, 331883, 331884, 331885, 331886, 331887, 331888, 331889, 331890, 331891, 331892, 331893, 331894, 331895, 331896, 331897, 331898, 331899, 331900, 331901, 331902, 331903, 331904, 331905, 331906, 331907, 331908, 331909, 331910, 331911, 331912, 331913, 331914, 331915, 331916, 331917, 331918, 331919, 331920, 331921, 331922, 331923, 331924, 331925, 331926, 331927, 331928, 331929, 331930, 331931, 331932, 331933, 331934, 331935, 331936, 331937, 331938, 331939, 331940, 331941, 331942, 331943, 331944, 331945, 331946, 331947, 331948, 331949, 331950, 331951, 331952, 331953, 331954, 331955, 331956, 331957, 331958, 331959, 331960, 331961, 331962, 331963, 331964, 331965, 331966, 331967, 331968, 331969, 331970, 331971, 331972, 331973, 331974, 331975, 331976, 331977, 331978, 331979, 331980, 331981, 331982, 331983, 331984, 331985, 331986, 331987, 331988, 331989, 331990, 331991, 331992, 331993, 331994, 331995, 331996, 331997, 331998, 331999, 332000, 332001, 332002, 332003, 332004, 332005, 332006, 332007, 332008, 332009, 332010, 332011, 332012, 332013, 332014, 332015, 332016, 332017, 332018, 332019, 332020, 332021, 332022, 332023, 332024, 332025, 332026, 332027, 332028, 332029, 332030, 332031, 332032, 332033, 332034, 332035, 332036, 332037, 332038, 332039, 332040, 332041, 332042, 332043, 332044, 332045, 332046, 332047, 332048, 332049, 332050, 332051, 332052, 332053, 332054, 332055, 332056, 332057, 332058, 332059, 332060, 332061, 332062, 332063, 332064, 332065, 332066, 332067, 332068, 332069, 332070, 332071, 332072, 332073, 332074, 332075, 332076, 332077, 332078, 332079, 332080, 332081, 332082, 332083, 332084, 332085, 332086, 332087, 332088, 332089, 332090, 332091, 332092, 332093, 332094, 332095, 332096, 332097, 332098, 332099, 332100, 332101, 332102, 332103, 332104, 332105, 332106, 332107, 332108, 332109, 332110, 332111, 332112, 332113, 332114, 332115, 332116, 332117, 332118, 332119, 332120, 332121, 332122, 332123, 332124, 332125, 332126, 332127, 332128, 332129, 332130, 332131, 332132, 332133, 332134, 332135, 332136, 332137, 332138, 332139, 332140, 332141, 332142, 332143, 332144, 332145, 332146, 332147, 332148, 332149, 332150, 332151, 332152, 332153, 332154, 332155, 332156, 332157, 332158, 332159, 332160, 332161, 332162, 332163, 332164, 332165, 332166, 332167, 332168, 332169, 332170, 332171, 332172, 332173, 332174, 332175, 332176, 332177, 332178, 332179, 332180, 332181, 332182, 332183, 332184, 332185, 332186, 332187, 332188, 332189, 332190, 332191, 332192, 332193, 332194, 332195, 332196, 332197, 332198, 332199, 332200, 332201, 332202, 332203, 332204, 332205, 332206, 332207, 332208, 332209, 332210, 332211, 332212, 332213, 332214, 332215, 332216, 332217, 332218, 332219, 332220, 332221, 332222, 332223, 332224, 332225, 332226, 332227, 332228, 332229, 332230, 332231, 332232, 332233, 332234, 332235, 332236, 332237, 332238, 332239, 332240, 332241, 332242, 332243, 332244, 332245, 332246, 332247, 332248, 332249, 332250, 332251, 332252, 332253, 332254, 332255, 332256, 332257, 332258, 332259, 332260, 332261, 332262, 332263, 332264, 332265, 332266, 332267, 332268, 332269, 332270, 332271, 332272, 332273, 332274, 332275, 332276, 332277, 332278, 332279, 332280, 332281, 332282, 332283, 332284, 332285, 332286, 332287, 332288, 332289, 332290, 332291, 332292, 332293, 332294, 332295, 332296, 332297, 332298, 332299, 332300, 332301, 332302, 332303, 332304, 332305, 332306, 332307, 332308, 332309, 332310, 332311, 332312, 332313, 332314, 332315, 332316, 332317, 332318, 332319, 332320, 332321, 332322, 332323, 332324, 332325, 332326, 332327, 332328, 332329, 332330, 332331, 332332, 332333, 332334, 332335, 332336, 332337, 332338, 332339, 332340, 332341, 332342, 332343, 332344, 332345, 332346, 332347, 332348, 332349, 332350, 332351, 332352, 332353, 332354, 332355, 332356, 332357, 332358, 332359, 332360, 332361, 332362, 332363, 332364, 332365, 332366, 332367, 332368, 332369, 332370, 332371, 332372, 332373, 332374, 332375, 332376, 332377, 332378, 332379, 332380, 332381, 332382, 332383, 332384, 332385, 332386, 332387, 332388, 332389, 332390, 332391, 332392, 332393, 332394, 332395, 332396, 332397, 332398, 332399, 332400, 332401, 332402, 332403, 332404, 332405, 332406, 332407, 332408, 332409, 332410, 332411, 332412, 332413, 332414, 332415, 332416, 332417, 332418, 332419, 332420, 332421, 332422, 332423, 332424, 332425, 332426, 332427, 332428, 332429, 332430, 332431, 332432, 332433, 332434, 332435, 332436, 332437, 332438, 332439, 332440, 332441, 332442, 332443, 332444, 332445, 332446, 332447, 332448, 332449, 332450, 332451, 332452, 332453, 332454, 332455, 332456, 332457, 332458, 332459, 332460, 332461, 332462, 332463, 332464, 332465, 332466, 332467, 332468, 332469, 332470, 332471, 332472, 332473, 332474, 332475, 332476, 332477, 332478, 332479, 332480, 332481, 332482, 332483, 332484, 332485, 332486, 332487, 332488, 332489, 332490, 332491, 332492, 332493, 332494, 332495, 332496, 332497, 332498, 332499, 332500, 332501, 332502, 332503, 332504, 332505, 332506, 332507, 332508, 332509, 332510, 332511, 332512, 332513, 332514, 332515, 332516, 332517, 332518, 332519, 332520, 332521, 332522, 332523, 332524, 332525, 332526, 332527, 332528, 332529, 332530, 332531, 332532, 332533, 332534, 332535, 332536, 332537, 332538, 332539, 332540, 332541, 332542, 332543, 332544, 332545, 332546, 332547, 332548, 332549, 332550, 332551, 332552, 332553, 332554, 332555, 332556, 332557, 332558, 332559, 332560, 332561, 332562, 332563, 332564, 332565, 332566, 332567, 332568, 332569, 332570, 332571, 332572, 332573, 332574, 332575, 332576, 332577, 332578, 332579, 332580, 332581, 332582, 332583, 332584, 332585, 332586, 332587, 332588, 332589, 332590, 332591, 332592, 332593, 332594, 332595, 332596, 332597, 332598, 332599, 332600, 332601, 332602, 332603, 332604, 332605, 332606, 332607, 332608, 332609, 332610, 332611, 332612, 332613, 332614, 332615, 332616, 332617, 332618, 332619, 332620, 332621, 332622, 332623, 332624, 332625, 332626, 332627, 332628, 332629, 332630, 332631, 332632, 332633, 332634, 332635, 332636, 332637, 332638, 332639, 332640, 332641, 332642, 332643, 332644, 332645, 332646, 332647, 332648, 332649, 332650, 332651, 332652, 332653, 332654, 332655, 332656, 332657, 332658, 332659, 332660, 332661, 332662, 332663, 332664, 332665, 332666, 332667, 332668, 332669, 332670, 332671, 332672, 332673, 332674, 332675, 332676, 332677, 332678, 332679, 332680, 332681, 332682, 332683, 332684, 332685, 332686, 332687, 332688, 332689, 332690, 332691, 332692, 332693, 332694, 332695, 332696, 332697, 332698, 332699, 332700, 332701, 332702, 332703, 332704, 332705, 332706, 332707, 332708, 332709, 332710, 332711, 332712, 332713, 332714, 332715, 332716, 332717, 332718, 332719, 332720, 332721, 332722, 332723, 332724, 332725, 332726, 332727, 332728, 332729, 332730, 332731, 332732, 332733, 332734, 332735, 332736, 332737, 332738, 332739, 332740, 332741, 332742, 332743, 332744, 332745, 332746, 332747, 332748, 332749, 332750, 332751, 332752, 332753, 332754, 332755, 332756, 332757, 332758, 332759, 332760, 332761, 332762, 332763, 332764, 332765, 332766, 332767, 332768, 332769, 332770, 332771, 332772, 332773, 332774, 332775, 332776, 332777, 332778, 332779, 332780, 332781, 332782, 332783, 332784, 332785, 332786, 332787, 332788, 332789, 332790, 332791, 332792, 332793, 332794, 332795, 332796, 332797, 332798, 332799, 332800, 332801, 332802, 332803, 332804, 332805, 332806, 332807, 332808, 332809, 332810, 332811, 332812, 332813, 332814, 332815, 332816, 332817, 332818, 332819, 332820, 332821, 332822, 332823, 332824, 332825, 332826, 332827, 332828, 332829, 332830, 332831, 332832, 332833, 332834, 332835, 332836, 332837, 332838, 332839, 332840, 332841, 332842, 332843, 332844, 332845, 332846, 332847, 332848, 332849, 332850, 332851, 332852, 332853, 332854, 332855, 332856, 332857, 332858, 332859, 332860, 332861, 332862, 332863, 332864, 332865, 332866, 332867, 332868, 332869, 332870, 332871, 332872, 332873, 332874, 332875, 332876, 332877, 332878, 332879, 332880, 332881, 332882, 332883, 332884, 332885, 332886, 332887, 332888, 332889, 332890, 332891, 332892, 332893, 332894, 332895, 332896, 332897, 332898, 332899, 332900, 332901, 332902, 332903, 332904, 332905, 332906, 332907, 332908, 332909, 332910, 332911, 332912, 332913, 332914, 332915, 332916, 332917, 332918, 332919, 332920, 332921, 332922, 332923, 332924, 332925, 332926, 332927, 332928, 332929, 332930, 332931, 332932, 332933, 332934, 332935, 332936, 332937, 332938, 332939, 332940, 332941, 332942, 332943, 332944, 332945, 332946, 332947, 332948, 332949, 332950, 332951, 332952, 332953, 332954, 332955, 332956, 332957, 332958, 332959, 332960, 332961, 332962, 332963, 332964, 332965, 332966, 332967, 332968, 332969, 332970, 332971, 337708, 337709, 337710, 337711, 337712, 337713, 337714, 337715, 337716, 337717, 337718, 337719, 337720, 337721, 337722, 337723, 337724, 337725, 337726, 337727, 337728, 337729, 337730, 337731, 337732, 337733, 337734, 337735, 337736, 337737, 337738, 337739, 337740, 337741, 337742, 337743, 337744, 337745, 337746, 337747, 337748, 337749, 337750, 337751, 337752, 337753, 337754, 337755, 337756, 337757, 337758, 337759, 337760, 337761, 337762, 337763, 337764, 337765, 337766, 337767, 337768, 337769, 337770, 337771, 337772, 337773, 337774, 337775, 337776, 337777, 337778, 337779, 337780, 337781, 337782, 337783, 337784, 337785, 337786, 337787, 337788, 337789, 337790, 337791, 337792, 337793, 337794, 337795, 337796, 337797, 337798, 337799, 337800, 337801, 337802, 337803, 337804, 337805, 337806, 337807, 337808, 337809, 337810, 337811, 337812, 337813, 337814, 337815, 337816, 337817, 337818, 337819, 337820, 337821, 337822, 337823, 337824, 337825, 337826, 337827, 337828, 337829, 337830, 337831, 337832, 337833, 337834, 337835, 337836, 337837, 337838, 337839, 337840, 337841]
2025-07-27 15:15:45.193 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting type-aware smart filtering. Initial total topics: 2422. Required type counts from request: {multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Initial topic distribution by type (from input list):
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'multiple': 421 topics
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'judge': 602 topics
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'choice': 1397 topics
2025-07-27 15:15:45.195 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics (present in input, but not in requiredTypeCounts or map is null)
2025-07-27 15:15:45.196 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'short': 0 topics in input (but 0 required)
2025-07-27 15:15:45.196 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'fill': 0 topics in input (but 0 required)
2025-07-27 15:15:45.196 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'multiple' - required: 20, available in input: 421
2025-07-27 15:15:45.196 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 421 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:15:45.197 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:15:45.197 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.197 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.197 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 62 topics remaining after reuse interval filtering (out of 62 initial topics).
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.198 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 81 topics remaining after reuse interval filtering (out of 81 initial topics).
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.198 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 138 topics remaining after reuse interval filtering (out of 138 initial topics).
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.198 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 140 topics remaining after reuse interval filtering (out of 140 initial topics).
2025-07-27 15:15:45.198 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 421 initial).
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'multiple' contributed 200 topics after smartFilterForType. Total in result now: 200
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'judge' - required: 20, available in input: 602
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 602 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.199 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 63 topics remaining after reuse interval filtering (out of 63 initial topics).
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.199 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 104 topics remaining after reuse interval filtering (out of 104 initial topics).
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.199 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.199 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 220 topics remaining after reuse interval filtering (out of 220 initial topics).
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.200 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 215 topics remaining after reuse interval filtering (out of 215 initial topics).
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 602 initial).
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'judge' contributed 200 topics after smartFilterForType. Total in result now: 400
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'choice' - required: 60, available in input: 1397
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 1397 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.200 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 236 topics remaining after reuse interval filtering (out of 236 initial topics).
2025-07-27 15:15:45.200 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.200 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 249 topics remaining after reuse interval filtering (out of 249 initial topics).
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.201 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 461 topics remaining after reuse interval filtering (out of 461 initial topics).
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:15:45.201 will be kept. Min reuse interval: 1 days.
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 451 topics remaining after reuse interval filtering (out of 451 initial topics).
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 1397 initial).
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'choice' contributed 200 topics after smartFilterForType. Total in result now: 600
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type-aware filtering completed. Final total topics selected: 600. Initial total topics was: 2422
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 3: After Type-Aware Smart Diversity Filter (with KP-level control), 600 topics available for GA (input size was 2422). MinReuseIntervalDays: null
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Diversity filtering already applied via smartFilter. 600 topics remain.
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4a: Checking for precise allocation requirements...
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Checking knowledge point configurations for short answer requirements...
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 602: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:15:45.201 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 603: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 269: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 270: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Overall hasShortAnswerConfiguration: false
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4b: Executing genetic algorithm for basic types with 600 candidate topics...
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA will handle basic types only: {multiple=20, judge=20, choice=60, fill=0}
2025-07-27 15:15:45.202 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA target score: 300 (total 300 - short answer 0)
2025-07-27 15:15:45.212 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Created execution record with ID: 11
2025-07-27 15:15:45.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA started - Population: 300, Generations: 200, Questions: 600
2025-07-27 15:15:45.337 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generated 90 greedy seed chromosomes
2025-07-27 15:15:45.342 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized enhanced population with 300 chromosomes
2025-07-27 15:15:45.343 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Intelligent population initialized: 300 total chromosomes
2025-07-27 15:15:45.511 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 1: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.40387087868635146ms, NoImprove=0.3924664276351188, Elite=0.529361403548701
2025-07-27 15:15:45.671 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 2: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.40644867465871004ms, NoImprove=0.3957655647681044, Elite=0.5169300503562545
2025-07-27 15:15:45.792 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 15:15:45.804 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=41, pages=9, current=1, size=5, records=5
2025-07-27 15:15:45.873 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 3: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4089876712359786ms, NoImprove=0.3983591503326084, Elite=0.5126124754392615
2025-07-27 15:15:46.020 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 4: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4193690003543388ms, NoImprove=0.4005555160370494, Elite=0.4956034493411244
2025-07-27 15:15:46.192 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 5: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4193690003543388ms, NoImprove=0.40322892657117637, Elite=0.4825401208766118
2025-07-27 15:15:46.913 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 10: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4240214499067005ms, NoImprove=0.4122975828908381, Elite=0.3924081865567374
2025-07-27 15:15:47.944 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 20: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4244736381527949ms, NoImprove=0.4148458526149582, Elite=0.036881217378699094
2025-07-27 15:15:48.973 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 30: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4260572483224992ms, NoImprove=0.4136016887451015, Elite=0.056914003805633544
2025-07-27 15:15:49.983 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 40: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42664332497429314ms, NoImprove=0.4148919781113435, Elite=0.05488007286715164
2025-07-27 15:15:50.990 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 50: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4272272805063552ms, NoImprove=0.41502841520960687, Elite=0.0754739856771624
2025-07-27 15:15:52.069 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 60: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4278694168365462ms, NoImprove=0.41530555706827343, Elite=0.08164125685479748
2025-07-27 15:15:53.129 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 70: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42810659143614765ms, NoImprove=0.4173592154679939, Elite=0.05773534894492102
2025-07-27 15:15:54.182 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 80: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42823021514735793ms, NoImprove=0.4185876763519578, Elite=0.008234730723707473
2025-07-27 15:15:55.203 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 90: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42825831540447956ms, NoImprove=0.4191067906514584, Elite=0.013665538635902358
2025-07-27 15:15:56.305 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 100: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4284777338677346ms, NoImprove=0.4176415710111279, Elite=0.038426577562889336
2025-07-27 15:15:57.357 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 110: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4285545746729149ms, NoImprove=0.4185318201126279, Elite=0.023971779771833054
2025-07-27 15:15:58.439 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 120: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42877525701507424ms, NoImprove=0.41821605406493717, Elite=0.04966747326225511
2025-07-27 15:15:59.568 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 130: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4288297877442047ms, NoImprove=0.4196704334326657, Elite=0.020203143755501238
2025-07-27 15:16:00.677 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 140: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4289139420810883ms, NoImprove=0.4186684349931182, Elite=0.014643387077817056
2025-07-27 15:16:01.799 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 150: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4289776570127656ms, NoImprove=0.4192486059892963, Elite=0.038891147405437074
2025-07-27 15:16:02.816 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 160: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42913837400213084ms, NoImprove=0.4194402971742585, Elite=0.014964857772317597
2025-07-27 15:16:03.887 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 170: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4292335139738124ms, NoImprove=0.4186967010829602, Elite=0.04102850601175706
2025-07-27 15:16:05.024 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 180: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.42934665378275927ms, NoImprove=0.42055168926845843, Elite=0.018681150250882465
2025-07-27 15:16:05.287 [http-nio-8081-exec-8] WARN  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Global timeout reached at generation 183
2025-07-27 15:16:05.293 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA completed in 20071ms. Best fitness: {:.4f}, Selected: 0.42934665378275927 topics
2025-07-27 15:16:05.293 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Algorithm performance metrics: {avg_diversity=0.05931533449197829, convergence_rate=3.7949986305772424E-4, total_time_ms=20071, generations=182, final_diversity=0.017396794449208903}
2025-07-27 15:16:05.310 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined results: 100 basic type topics + 0 short answer topics = 100 total topics
2025-07-27 15:16:05.310 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined algorithm selected 100 topics with type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:05.310 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined result: All type constraints met!
2025-07-27 15:16:05.310 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 300, 同时保持题型分布
2025-07-27 15:16:05.311 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:05.312 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:05.312 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:05.312 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 100 topics
2025-07-27 15:16:05.313 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:05.313 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:05.313 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - After DP adjustment: Type distribution = {multiple=20, judge=20, choice=60}
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP score adjustment complete. Final total score = 300, with type distribution preserved.
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP adjustment completed in 3 ms
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={multiple=20, judge=20, choice=60}, 目标={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 各题型题目数量满足要求，无需调整
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 2422 topics
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:05.314 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final DP adjustment succeeded. totalScore=300, typeCounts={singleChoice=60, judgment=20, multipleChoice=20}.
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 100 topics.
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 100. Requested global counts (for warning reference): {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:05.315 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:05.316 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:05.316 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:05.318 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:05.318 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:05.328 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:05.331 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:05.331 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:05.331 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 300) after enforcing type counts...
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - DPAdjuster will receive targetTypeCounts (normalized keys): {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:05.332 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:05.335 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:05.335 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:05.345 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:05.347 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:05.347 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:05.347 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:05.349 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:05.349 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - After final enforcement: 100 topics, calculated score: 300 (target score: 300)
2025-07-27 15:16:05.351 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Final type counts: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}, Requested counts: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:05.408 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 307. Title: 测试试卷 (第1套)
2025-07-27 15:16:05.545 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 100 topics.
2025-07-27 15:16:05.546 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.KnowledgePointUsageTracker - KnowledgePointUsageTracker: Updating usage stats for 4 knowledge points
2025-07-27 15:16:05.685 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 第 1 套试卷生成成功: 测试试卷 (第1套)
2025-07-27 15:16:05.685 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='测试试卷 (第2套)', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={medium=0.5, hard=0.2, easy=0.3}
2025-07-27 15:16:05.685 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(paperId=null, knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], title=测试试卷 (第2套), totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={medium=0.5, hard=0.2, easy=0.3}, topicTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-07-27 15:16:05.685 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Converted type counts for entire process: frontend={SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0} -> database={multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:16:05.704 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 602 contributed 361 questions
2025-07-27 15:16:05.729 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 603 contributed 434 questions
2025-07-27 15:16:05.776 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 269 contributed 821 questions
2025-07-27 15:16:05.816 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 270 contributed 806 questions
2025-07-27 15:16:05.819 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 当前题目池已满足所有题型要求，无需扩展
2025-07-27 15:16:05.820 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final topic pool size after potential expansion: 2422. Target global counts: {SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:05.839 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 2422 topics. IDs: [384948, 384949, 384950, 384951, 384952, 384953, 384954, 384955, 384956, 384957, 384958, 384959, 384960, 384961, 384962, 384963, 384964, 384965, 384966, 384967, 384968, 384969, 384970, 384971, 384972, 384973, 384974, 384975, 384976, 384977, 384978, 384979, 384980, 384981, 384982, 384983, 384984, 384985, 384986, 384987, 384988, 384989, 384990, 384991, 384992, 384993, 384994, 384995, 384996, 384997, 384998, 384999, 385000, 385001, 385002, 385003, 385004, 385005, 385006, 385007, 385008, 385009, 385010, 385011, 385012, 385013, 385014, 385015, 385016, 385017, 385018, 385019, 385020, 385021, 385022, 385023, 385024, 385025, 385026, 385027, 385028, 385029, 385030, 385031, 385032, 385033, 385034, 385035, 385036, 385037, 385038, 385039, 385040, 385041, 385042, 385043, 385044, 385045, 385046, 385047, 385048, 385049, 385050, 385051, 385052, 385053, 385054, 385055, 385056, 385057, 385058, 385059, 385060, 385061, 385062, 385063, 385064, 385065, 385066, 385067, 385068, 385069, 385070, 385071, 385072, 385073, 385074, 385075, 385076, 385077, 385078, 385079, 385080, 385081, 385082, 385083, 385084, 385085, 385086, 385087, 385088, 385089, 385090, 385091, 385092, 385093, 385094, 385095, 385096, 385097, 385098, 385099, 385100, 385101, 385102, 385103, 385104, 385105, 385106, 385107, 385108, 385109, 385110, 385111, 385112, 385113, 385114, 385115, 385116, 385117, 385118, 385119, 385120, 385121, 385122, 385123, 385124, 385125, 385126, 385127, 385128, 385129, 385130, 385131, 385132, 385133, 385134, 385135, 385136, 385137, 385138, 385139, 385140, 385141, 385142, 385143, 385144, 385145, 385146, 385147, 385148, 385149, 385150, 385151, 385152, 385153, 385154, 385155, 385156, 385157, 385158, 385159, 385160, 385161, 385162, 385163, 385164, 385165, 385166, 385167, 385168, 385169, 385170, 385171, 385172, 385173, 385174, 385175, 385176, 385177, 385178, 385179, 385180, 385181, 385182, 385183, 385184, 385185, 385186, 385187, 385188, 385189, 385190, 385191, 385192, 385193, 385194, 385195, 385196, 385197, 385198, 385199, 385200, 385201, 385202, 385203, 385204, 385205, 385206, 385207, 385208, 385209, 385210, 385211, 385212, 385213, 385214, 385215, 385216, 385217, 385218, 385219, 385220, 385221, 385222, 385223, 385224, 385225, 385226, 385227, 385228, 385229, 385230, 385231, 385232, 385233, 385234, 385235, 385236, 385237, 385238, 385239, 385240, 385241, 385242, 385243, 385244, 385245, 385246, 385247, 385248, 385249, 385250, 385251, 385252, 385253, 385254, 385255, 385256, 385257, 385258, 385259, 385260, 385261, 385262, 385263, 385264, 385265, 385266, 385267, 385268, 385269, 385270, 385271, 385272, 385273, 385274, 385275, 385276, 385277, 385278, 385279, 385280, 385281, 385282, 385283, 385284, 385285, 385286, 385287, 385288, 385289, 385290, 385291, 385292, 385293, 385294, 385295, 385296, 385297, 385298, 385299, 385300, 385301, 385302, 385303, 385304, 385305, 385306, 385307, 385308, 384514, 384515, 384516, 384517, 384518, 384519, 384520, 384521, 384522, 384523, 384524, 384525, 384526, 384527, 384528, 384529, 384530, 384531, 384532, 384533, 384534, 384535, 384536, 384537, 384538, 384539, 384540, 384541, 384542, 384543, 384544, 384545, 384546, 384547, 384548, 384549, 384550, 384551, 384552, 384553, 384554, 384555, 384556, 384557, 384558, 384559, 384560, 384561, 384562, 384563, 384564, 384565, 384566, 384567, 384568, 384569, 384570, 384571, 384572, 384573, 384574, 384575, 384576, 384577, 384578, 384579, 384580, 384581, 384582, 384583, 384584, 384585, 384586, 384587, 384588, 384589, 384590, 384591, 384592, 384593, 384594, 384595, 384596, 384597, 384598, 384599, 384600, 384601, 384602, 384603, 384604, 384605, 384606, 384607, 384608, 384609, 384610, 384611, 384612, 384613, 384614, 384615, 384616, 384617, 384618, 384619, 384620, 384621, 384622, 384623, 384624, 384625, 384626, 384627, 384628, 384629, 384630, 384631, 384632, 384633, 384634, 384635, 384636, 384637, 384638, 384639, 384640, 384641, 384642, 384643, 384644, 384645, 384646, 384647, 384648, 384649, 384650, 384651, 384652, 384653, 384654, 384655, 384656, 384657, 384658, 384659, 384660, 384661, 384662, 384663, 384664, 384665, 384666, 384667, 384668, 384669, 384670, 384671, 384672, 384673, 384674, 384675, 384676, 384677, 384678, 384679, 384680, 384681, 384682, 384683, 384684, 384685, 384686, 384687, 384688, 384689, 384690, 384691, 384692, 384693, 384694, 384695, 384696, 384697, 384698, 384699, 384700, 384701, 384702, 384703, 384704, 384705, 384706, 384707, 384708, 384709, 384710, 384711, 384712, 384713, 384714, 384715, 384716, 384717, 384718, 384719, 384720, 384721, 384722, 384723, 384724, 384725, 384726, 384727, 384728, 384729, 384730, 384731, 384732, 384733, 384734, 384735, 384736, 384737, 384738, 384739, 384740, 384741, 384742, 384743, 384744, 384745, 384746, 384747, 384748, 384749, 384750, 384751, 384752, 384753, 384754, 384755, 384756, 384757, 384758, 384759, 384760, 384761, 384762, 384763, 384764, 384765, 384766, 384767, 384768, 384769, 384770, 384771, 384772, 384773, 384774, 384775, 384776, 384777, 384778, 384779, 384780, 384781, 384782, 384783, 384784, 384785, 384786, 384787, 384788, 384789, 384790, 384791, 384792, 384793, 384794, 384795, 384796, 384797, 384798, 384799, 384800, 384801, 384802, 384803, 384804, 384805, 384806, 384807, 384808, 384809, 384810, 384811, 384812, 384813, 384814, 384815, 384816, 384817, 384818, 384819, 384820, 384821, 384822, 384823, 384824, 384825, 384826, 384827, 384828, 384829, 384830, 384831, 384832, 384833, 384834, 384835, 384836, 384837, 384838, 384839, 384840, 384841, 384842, 384843, 384844, 384845, 384846, 384847, 384848, 384849, 384850, 384851, 384852, 384853, 384854, 384855, 384856, 384857, 384858, 384859, 384860, 384861, 384862, 384863, 384864, 384865, 384866, 384867, 384868, 384869, 384870, 384871, 384872, 384873, 384874, 384875, 384876, 384877, 384878, 384879, 384880, 384881, 384882, 384883, 384884, 384885, 384886, 384887, 384888, 384889, 384890, 384891, 384892, 384893, 384894, 384895, 384896, 384897, 384898, 384899, 384900, 384901, 384902, 384903, 384904, 384905, 384906, 384907, 384908, 384909, 384910, 384911, 384912, 384913, 384914, 384915, 384916, 384917, 384918, 384919, 384920, 384921, 384922, 384923, 384924, 384925, 384926, 384927, 384928, 384929, 384930, 384931, 384932, 384933, 384934, 384935, 384936, 384937, 384938, 384939, 384940, 384941, 384942, 384943, 384944, 384945, 384946, 384947, 331479, 331480, 331481, 331482, 331483, 331484, 331485, 331486, 331487, 331488, 331489, 331490, 331491, 331492, 331493, 331494, 331495, 331496, 331497, 331498, 331499, 331500, 331501, 331502, 331503, 331504, 331505, 331506, 331507, 331508, 331509, 331510, 331511, 331512, 331513, 331514, 331515, 331516, 331517, 331518, 331519, 331520, 331521, 331522, 331523, 331524, 331525, 331526, 331527, 331528, 331529, 331530, 331531, 331532, 331533, 331534, 331535, 331536, 331537, 331538, 331539, 331540, 331541, 331542, 331543, 331544, 331545, 331546, 331547, 331548, 331549, 331550, 331551, 331552, 331553, 331554, 331555, 331556, 331557, 331558, 331559, 331560, 331561, 331562, 331563, 331564, 331565, 331566, 331567, 331568, 331569, 331570, 331571, 331572, 331573, 331574, 331575, 331576, 331577, 331578, 331579, 331580, 331581, 331582, 331583, 331584, 331585, 331586, 331587, 331588, 331589, 331590, 331591, 331592, 331593, 331594, 331595, 331596, 331597, 331598, 331599, 331600, 331601, 331602, 331603, 331604, 331605, 331606, 331607, 331608, 331609, 331610, 331611, 331612, 331613, 331614, 331615, 331616, 331617, 331618, 331619, 331620, 331621, 331622, 331623, 331624, 331625, 331626, 331627, 331628, 331629, 331630, 331631, 331632, 331633, 331634, 331635, 331636, 331637, 331638, 331639, 331640, 331641, 331642, 331643, 331644, 331645, 331646, 331647, 331648, 331649, 331650, 331651, 331652, 331653, 331654, 331655, 331656, 331657, 331658, 331659, 331660, 331661, 331662, 331663, 331664, 331665, 331666, 331667, 331668, 331669, 331670, 331671, 331672, 331673, 331674, 331675, 331676, 331677, 331678, 331679, 331680, 331681, 331682, 331683, 331684, 331685, 331686, 331687, 331688, 331689, 331690, 331691, 331692, 331693, 331694, 331695, 331696, 331697, 331698, 331699, 331700, 331701, 331702, 331703, 331704, 331705, 331706, 331707, 331708, 331709, 331710, 331711, 331712, 331713, 331714, 331715, 331716, 331717, 331718, 331719, 331720, 331721, 331722, 331723, 331724, 331725, 331726, 331727, 331728, 331729, 331730, 331731, 331732, 331733, 331734, 331735, 331736, 331737, 331738, 331739, 331740, 331741, 331742, 331743, 331744, 331745, 331746, 331747, 331748, 331749, 331750, 331751, 331752, 331753, 331754, 331755, 331756, 331757, 331758, 331759, 331760, 331761, 331762, 331763, 331764, 331765, 331766, 331767, 331768, 331769, 331770, 331771, 331772, 331773, 331774, 331775, 331776, 331777, 331778, 331779, 331780, 331781, 331782, 331783, 331784, 331785, 331786, 331787, 331788, 331789, 331790, 331791, 331792, 331793, 331794, 331795, 331796, 331797, 331798, 331799, 331800, 331801, 331802, 331803, 331804, 331805, 331806, 331807, 331808, 331809, 331810, 331811, 331812, 331813, 331814, 331815, 331816, 331817, 331818, 331819, 331820, 331821, 331822, 331823, 331824, 331825, 331826, 331827, 331828, 331829, 331830, 331831, 331832, 331833, 331834, 331835, 331836, 331837, 331838, 331839, 331840, 331841, 331842, 331843, 331844, 331845, 331846, 331847, 331848, 331849, 331850, 331851, 331852, 331853, 331854, 331855, 331856, 331857, 331858, 331859, 331860, 331861, 331862, 331863, 331864, 331865, 331866, 331867, 331868, 331869, 331870, 331871, 331872, 331873, 331874, 331875, 331876, 331877, 331878, 331879, 331880, 331881, 331882, 331883, 331884, 331885, 331886, 331887, 331888, 331889, 331890, 331891, 331892, 331893, 331894, 331895, 331896, 331897, 331898, 331899, 331900, 331901, 331902, 331903, 331904, 331905, 331906, 331907, 331908, 331909, 331910, 331911, 331912, 331913, 331914, 331915, 331916, 331917, 331918, 331919, 331920, 331921, 331922, 331923, 331924, 331925, 331926, 331927, 331928, 331929, 331930, 331931, 331932, 331933, 331934, 331935, 331936, 331937, 331938, 331939, 331940, 331941, 331942, 331943, 331944, 331945, 331946, 331947, 331948, 331949, 331950, 331951, 331952, 331953, 331954, 331955, 331956, 331957, 331958, 331959, 331960, 331961, 331962, 331963, 331964, 331965, 331966, 331967, 331968, 331969, 331970, 331971, 331972, 331973, 331974, 331975, 331976, 331977, 331978, 331979, 331980, 331981, 331982, 331983, 331984, 331985, 331986, 331987, 331988, 331989, 331990, 331991, 331992, 331993, 331994, 331995, 331996, 331997, 331998, 331999, 332000, 332001, 332002, 332003, 332004, 332005, 332006, 332007, 332008, 332009, 332010, 332011, 332012, 332013, 332014, 332015, 332016, 332017, 332018, 332019, 332020, 332021, 332022, 332023, 332024, 332025, 332026, 332027, 332028, 332029, 332030, 332031, 332032, 332033, 332034, 332035, 332036, 332037, 332038, 332039, 332040, 332041, 332042, 332043, 332044, 332045, 332046, 332047, 332048, 332049, 332050, 332051, 332052, 332053, 332054, 332055, 332056, 332057, 332058, 332059, 332060, 332061, 332062, 332063, 332064, 332065, 332066, 332067, 332068, 332069, 332070, 332071, 332072, 332073, 332074, 332075, 332076, 332077, 332078, 332079, 332080, 332081, 332082, 332083, 332084, 332085, 332086, 332087, 332088, 332089, 332090, 332091, 332092, 332093, 332094, 332095, 332096, 332097, 332098, 332099, 332100, 332101, 332102, 332103, 332104, 332105, 332106, 332107, 332108, 332109, 332110, 332111, 332112, 332113, 332114, 332115, 332116, 332117, 332118, 332119, 332120, 332121, 332122, 332123, 332124, 332125, 332126, 332127, 332128, 332129, 332130, 332131, 332132, 332133, 332134, 332135, 332136, 332137, 332138, 332139, 332140, 332141, 332142, 332143, 332144, 332145, 332146, 332147, 332148, 332149, 332150, 332151, 332152, 332153, 332154, 332155, 332156, 332157, 332158, 332159, 332160, 332161, 332162, 332163, 332164, 332165, 332166, 332167, 332168, 332169, 332170, 332171, 332172, 332173, 332174, 332175, 332176, 332177, 332178, 332179, 332180, 332181, 332182, 332183, 332184, 332185, 332186, 332187, 332188, 332189, 332190, 332191, 332192, 332193, 332194, 332195, 332196, 332197, 332198, 332199, 332200, 332201, 332202, 332203, 332204, 332205, 332206, 332207, 332208, 332209, 332210, 332211, 332212, 332213, 332214, 332215, 332216, 332217, 332218, 332219, 332220, 332221, 332222, 332223, 332224, 332225, 332226, 332227, 332228, 332229, 332230, 332231, 332232, 332233, 332234, 332235, 332236, 332237, 332238, 332239, 332240, 332241, 332242, 332243, 332244, 332245, 332246, 332247, 332248, 332249, 332250, 332251, 332252, 332253, 332254, 332255, 332256, 332257, 332258, 332259, 332260, 332261, 332262, 332263, 332264, 332265, 332266, 332267, 332268, 332269, 332270, 332271, 332272, 332273, 332274, 332275, 332276, 332277, 332278, 332279, 332280, 332281, 332282, 332283, 332284, 332285, 332286, 332287, 332288, 332289, 332290, 332291, 332292, 332293, 332294, 332295, 332296, 332297, 332298, 332299, 332300, 332301, 332302, 332303, 332304, 332305, 332306, 332307, 332308, 332309, 332310, 332311, 332312, 332313, 332314, 332315, 332316, 332317, 332318, 332319, 332320, 332321, 332322, 332323, 332324, 332325, 332326, 332327, 332328, 332329, 332330, 332331, 332332, 332333, 332334, 332335, 332336, 332337, 332338, 332339, 332340, 332341, 332342, 332343, 332344, 332345, 332346, 332347, 332348, 332349, 332350, 332351, 332352, 332353, 332354, 332355, 332356, 332357, 332358, 332359, 332360, 332361, 332362, 332363, 332364, 332365, 332366, 332367, 332368, 332369, 332370, 332371, 332372, 332373, 332374, 332375, 332376, 332377, 332378, 332379, 332380, 332381, 332382, 332383, 332384, 332385, 332386, 332387, 332388, 332389, 332390, 332391, 332392, 332393, 332394, 332395, 332396, 332397, 332398, 332399, 332400, 332401, 332402, 332403, 332404, 332405, 332406, 332407, 332408, 332409, 332410, 332411, 332412, 332413, 332414, 332415, 332416, 332417, 332418, 332419, 332420, 332421, 332422, 332423, 332424, 332425, 332426, 332427, 332428, 332429, 332430, 332431, 332432, 332433, 332434, 332435, 332436, 332437, 332438, 332439, 332440, 332441, 332442, 332443, 332444, 332445, 332446, 332447, 332448, 332449, 332450, 332451, 332452, 332453, 332454, 332455, 332456, 332457, 332458, 332459, 332460, 332461, 332462, 332463, 332464, 332465, 332466, 332467, 332468, 332469, 332470, 332471, 332472, 332473, 332474, 332475, 332476, 332477, 332478, 332479, 332480, 332481, 332482, 332483, 332484, 332485, 332486, 332487, 332488, 332489, 332490, 332491, 332492, 332493, 332494, 332495, 332496, 332497, 332498, 332499, 332500, 332501, 332502, 332503, 332504, 332505, 332506, 332507, 332508, 332509, 332510, 332511, 332512, 332513, 332514, 332515, 332516, 332517, 332518, 332519, 332520, 332521, 332522, 332523, 332524, 332525, 332526, 332527, 332528, 332529, 332530, 332531, 332532, 332533, 332534, 332535, 332536, 332537, 332538, 332539, 332540, 332541, 332542, 332543, 332544, 332545, 332546, 332547, 332548, 332549, 332550, 332551, 332552, 332553, 332554, 332555, 332556, 332557, 332558, 332559, 332560, 332561, 332562, 332563, 332564, 332565, 332566, 332567, 332568, 332569, 332570, 332571, 332572, 332573, 332574, 332575, 332576, 332577, 332578, 332579, 332580, 332581, 332582, 332583, 332584, 332585, 332586, 332587, 332588, 332589, 332590, 332591, 332592, 332593, 332594, 332595, 332596, 332597, 332598, 332599, 332600, 332601, 332602, 332603, 332604, 332605, 332606, 332607, 332608, 332609, 332610, 332611, 332612, 332613, 332614, 332615, 332616, 332617, 332618, 332619, 332620, 332621, 332622, 332623, 332624, 332625, 332626, 332627, 332628, 332629, 332630, 332631, 332632, 332633, 332634, 332635, 332636, 332637, 332638, 332639, 332640, 332641, 332642, 332643, 332644, 332645, 332646, 332647, 332648, 332649, 332650, 332651, 332652, 332653, 332654, 332655, 332656, 332657, 332658, 332659, 332660, 332661, 332662, 332663, 332664, 332665, 332666, 332667, 332668, 332669, 332670, 332671, 332672, 332673, 332674, 332675, 332676, 332677, 332678, 332679, 332680, 332681, 332682, 332683, 332684, 332685, 332686, 332687, 332688, 332689, 332690, 332691, 332692, 332693, 332694, 332695, 332696, 332697, 332698, 332699, 332700, 332701, 332702, 332703, 332704, 332705, 332706, 332707, 332708, 332709, 332710, 332711, 332712, 332713, 332714, 332715, 332716, 332717, 332718, 332719, 332720, 332721, 332722, 332723, 332724, 332725, 332726, 332727, 332728, 332729, 332730, 332731, 332732, 332733, 332734, 332735, 332736, 332737, 332738, 332739, 332740, 332741, 332742, 332743, 332744, 332745, 332746, 332747, 332748, 332749, 332750, 332751, 332752, 332753, 332754, 332755, 332756, 332757, 332758, 332759, 332760, 332761, 332762, 332763, 332764, 332765, 332766, 332767, 332768, 332769, 332770, 332771, 332772, 332773, 332774, 332775, 332776, 332777, 332778, 332779, 332780, 332781, 332782, 332783, 332784, 332785, 332786, 332787, 332788, 332789, 332790, 332791, 332792, 332793, 332794, 332795, 332796, 332797, 332798, 332799, 332800, 332801, 332802, 332803, 332804, 332805, 332806, 332807, 332808, 332809, 332810, 332811, 332812, 332813, 332814, 332815, 332816, 332817, 332818, 332819, 332820, 332821, 332822, 332823, 332824, 332825, 332826, 332827, 332828, 332829, 332830, 332831, 332832, 332833, 332834, 332835, 332836, 332837, 332838, 332839, 332840, 332841, 332842, 332843, 332844, 332845, 332846, 332847, 332848, 332849, 332850, 332851, 332852, 332853, 332854, 332855, 332856, 332857, 332858, 332859, 332860, 332861, 332862, 332863, 332864, 332865, 332866, 332867, 332868, 332869, 332870, 332871, 332872, 332873, 332874, 332875, 332876, 332877, 332878, 332879, 332880, 332881, 332882, 332883, 332884, 332885, 332886, 332887, 332888, 332889, 332890, 332891, 332892, 332893, 332894, 332895, 332896, 332897, 332898, 332899, 332900, 332901, 332902, 332903, 332904, 332905, 332906, 332907, 332908, 332909, 332910, 332911, 332912, 332913, 332914, 332915, 332916, 332917, 332918, 332919, 332920, 332921, 332922, 332923, 332924, 332925, 332926, 332927, 332928, 332929, 332930, 332931, 332932, 332933, 332934, 332935, 332936, 332937, 332938, 332939, 332940, 332941, 332942, 332943, 332944, 332945, 332946, 332947, 332948, 332949, 332950, 332951, 332952, 332953, 332954, 332955, 332956, 332957, 332958, 332959, 332960, 332961, 332962, 332963, 332964, 332965, 332966, 332967, 332968, 332969, 332970, 332971, 337708, 337709, 337710, 337711, 337712, 337713, 337714, 337715, 337716, 337717, 337718, 337719, 337720, 337721, 337722, 337723, 337724, 337725, 337726, 337727, 337728, 337729, 337730, 337731, 337732, 337733, 337734, 337735, 337736, 337737, 337738, 337739, 337740, 337741, 337742, 337743, 337744, 337745, 337746, 337747, 337748, 337749, 337750, 337751, 337752, 337753, 337754, 337755, 337756, 337757, 337758, 337759, 337760, 337761, 337762, 337763, 337764, 337765, 337766, 337767, 337768, 337769, 337770, 337771, 337772, 337773, 337774, 337775, 337776, 337777, 337778, 337779, 337780, 337781, 337782, 337783, 337784, 337785, 337786, 337787, 337788, 337789, 337790, 337791, 337792, 337793, 337794, 337795, 337796, 337797, 337798, 337799, 337800, 337801, 337802, 337803, 337804, 337805, 337806, 337807, 337808, 337809, 337810, 337811, 337812, 337813, 337814, 337815, 337816, 337817, 337818, 337819, 337820, 337821, 337822, 337823, 337824, 337825, 337826, 337827, 337828, 337829, 337830, 337831, 337832, 337833, 337834, 337835, 337836, 337837, 337838, 337839, 337840, 337841]
2025-07-27 15:16:05.842 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting type-aware smart filtering. Initial total topics: 2422. Required type counts from request: {multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Initial topic distribution by type (from input list):
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'multiple': 421 topics
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'judge': 602 topics
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'choice': 1397 topics
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics (present in input, but not in requiredTypeCounts or map is null)
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'short': 0 topics in input (but 0 required)
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'fill': 0 topics in input (but 0 required)
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'multiple' - required: 20, available in input: 421
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 421 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.843 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 58 topics remaining after reuse interval filtering (out of 62 initial topics).
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.843 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.843 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 81 topics remaining after reuse interval filtering (out of 81 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 133 topics remaining after reuse interval filtering (out of 138 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 129 topics remaining after reuse interval filtering (out of 140 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 421 initial).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'multiple' contributed 200 topics after smartFilterForType. Total in result now: 200
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'judge' - required: 20, available in input: 602
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 602 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 59 topics remaining after reuse interval filtering (out of 63 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 94 topics remaining after reuse interval filtering (out of 104 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 214 topics remaining after reuse interval filtering (out of 220 initial topics).
2025-07-27 15:16:05.844 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.844 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 215 topics remaining after reuse interval filtering (out of 215 initial topics).
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 602 initial).
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'judge' contributed 200 topics after smartFilterForType. Total in result now: 400
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'choice' - required: 60, available in input: 1397
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 1397 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.845 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 232 topics remaining after reuse interval filtering (out of 236 initial topics).
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.845 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 239 topics remaining after reuse interval filtering (out of 249 initial topics).
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.845 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.845 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 432 topics remaining after reuse interval filtering (out of 461 initial topics).
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:05.846 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 434 topics remaining after reuse interval filtering (out of 451 initial topics).
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 1397 initial).
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'choice' contributed 200 topics after smartFilterForType. Total in result now: 600
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type-aware filtering completed. Final total topics selected: 600. Initial total topics was: 2422
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 3: After Type-Aware Smart Diversity Filter (with KP-level control), 600 topics available for GA (input size was 2422). MinReuseIntervalDays: null
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Diversity filtering already applied via smartFilter. 600 topics remain.
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4a: Checking for precise allocation requirements...
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Checking knowledge point configurations for short answer requirements...
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 602: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 603: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 269: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 270: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Overall hasShortAnswerConfiguration: false
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4b: Executing genetic algorithm for basic types with 600 candidate topics...
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA will handle basic types only: {multiple=20, judge=20, choice=60, fill=0}
2025-07-27 15:16:05.846 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA target score: 300 (total 300 - short answer 0)
2025-07-27 15:16:05.852 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Created execution record with ID: 12
2025-07-27 15:16:05.858 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA started - Population: 300, Generations: 200, Questions: 600
2025-07-27 15:16:05.960 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generated 90 greedy seed chromosomes
2025-07-27 15:16:05.964 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized enhanced population with 300 chromosomes
2025-07-27 15:16:05.964 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Intelligent population initialized: 300 total chromosomes
2025-07-27 15:16:06.115 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 1: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49935169482314423ms, NoImprove=0.4890380500442957, Elite=0.5290740520497909
2025-07-27 15:16:06.263 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 2: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5024032572918913ms, NoImprove=0.49253096314332595, Elite=0.509788264586822
2025-07-27 15:16:06.405 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 3: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5065539062772771ms, NoImprove=0.4950145128116738, Elite=0.497081136985783
2025-07-27 15:16:06.536 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 4: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5087918003169175ms, NoImprove=0.49690977426733646, Elite=0.49959272403913135
2025-07-27 15:16:06.680 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 5: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5108723892415497ms, NoImprove=0.4991784711478157, Elite=0.48960918437502354
2025-07-27 15:16:07.362 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 10: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5171137110198433ms, NoImprove=0.5067949203295438, Elite=0.379158227916517
2025-07-27 15:16:08.399 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 20: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5237248570425378ms, NoImprove=0.5063413132842675, Elite=0.07899812163495719
2025-07-27 15:16:09.597 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 30: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5240038640143597ms, NoImprove=0.5115852556474605, Elite=0.03892860218004753
2025-07-27 15:16:10.869 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 40: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5240905721013737ms, NoImprove=0.5124026780461981, Elite=0.05246325022775645
2025-07-27 15:16:11.984 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 50: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5257829147678784ms, NoImprove=0.5122275789307147, Elite=0.03571325302024272
2025-07-27 15:16:13.054 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 60: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5260008373201457ms, NoImprove=0.5123958091000311, Elite=0.08114707963684878
2025-07-27 15:16:14.205 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 70: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5264551207167012ms, NoImprove=0.5125193062918553, Elite=0.09719509932620435
2025-07-27 15:16:15.269 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 80: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5269440938639296ms, NoImprove=0.5143846735331518, Elite=0.053342707412996695
2025-07-27 15:16:16.361 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 90: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5277040153691339ms, NoImprove=0.513450255351332, Elite=0.0068547432977181045
2025-07-27 15:16:17.955 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 100: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5281513652210242ms, NoImprove=0.5175187556636005, Elite=0.017519978466389797
2025-07-27 15:16:19.033 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 110: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5284903694147702ms, NoImprove=0.5155972060539548, Elite=0.03424510102455566
2025-07-27 15:16:20.084 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 120: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5285666868945743ms, NoImprove=0.5171478058079241, Elite=0.028864757316520526
2025-07-27 15:16:21.317 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 130: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5287622048391002ms, NoImprove=0.5182369728447042, Elite=0.006732941042582962
2025-07-27 15:16:22.346 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 140: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5291060624644882ms, NoImprove=0.5156027399071292, Elite=0.030676459753019063
2025-07-27 15:16:23.210 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination at generation 148
2025-07-27 15:16:23.210 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA completed in 17352ms. Best fitness: {:.4f}, Selected: 0.5291060624644882 topics
2025-07-27 15:16:23.210 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Algorithm performance metrics: {avg_diversity=0.08912403829013105, convergence_rate=4.277640974171571E-4, total_time_ms=17352, generations=148, final_diversity=0.0015686274509803908}
2025-07-27 15:16:23.220 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined results: 100 basic type topics + 0 short answer topics = 100 total topics
2025-07-27 15:16:23.220 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined algorithm selected 100 topics with type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:23.220 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined result: All type constraints met!
2025-07-27 15:16:23.220 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 300, 同时保持题型分布
2025-07-27 15:16:23.221 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:23.221 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:23.221 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:23.221 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 100 topics
2025-07-27 15:16:23.221 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:23.221 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:23.221 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - After DP adjustment: Type distribution = {multiple=20, judge=20, choice=60}
2025-07-27 15:16:23.221 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP score adjustment complete. Final total score = 300, with type distribution preserved.
2025-07-27 15:16:23.221 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP adjustment completed in 0 ms
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={multiple=20, judge=20, choice=60}, 目标={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 各题型题目数量满足要求，无需调整
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 2422 topics
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final DP adjustment succeeded. totalScore=300, typeCounts={singleChoice=60, judgment=20, multipleChoice=20}.
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 100 topics.
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 100. Requested global counts (for warning reference): {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:23.222 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:23.224 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:23.224 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:23.234 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:23.236 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:23.236 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:23.236 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 300) after enforcing type counts...
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - DPAdjuster will receive targetTypeCounts (normalized keys): {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:23.238 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:23.240 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:23.240 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:23.250 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:23.251 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:23.251 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:23.251 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:23.253 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:23.253 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - After final enforcement: 100 topics, calculated score: 300 (target score: 300)
2025-07-27 15:16:23.255 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Final type counts: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}, Requested counts: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:23.303 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 308. Title: 测试试卷 (第2套)
2025-07-27 15:16:23.413 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 100 topics.
2025-07-27 15:16:23.414 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.KnowledgePointUsageTracker - KnowledgePointUsageTracker: Updating usage stats for 4 knowledge points
2025-07-27 15:16:23.529 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 第 2 套试卷生成成功: 测试试卷 (第2套)
2025-07-27 15:16:23.529 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='测试试卷 (第3套)', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={medium=0.5, hard=0.2, easy=0.3}
2025-07-27 15:16:23.529 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(paperId=null, knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=14, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=18, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=270, knowledgeName=心理健康高中二年级, questionCount=34, includeShortAnswer=false, shortAnswerCount=0)], title=测试试卷 (第3套), totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={medium=0.5, hard=0.2, easy=0.3}, topicTypeCounts={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-07-27 15:16:23.530 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Converted type counts for entire process: frontend={SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0} -> database={multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:16:23.557 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 602 contributed 361 questions
2025-07-27 15:16:23.582 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 603 contributed 434 questions
2025-07-27 15:16:23.635 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 269 contributed 821 questions
2025-07-27 15:16:23.675 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 270 contributed 806 questions
2025-07-27 15:16:23.678 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 当前题目池已满足所有题型要求，无需扩展
2025-07-27 15:16:23.678 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final topic pool size after potential expansion: 2422. Target global counts: {SINGLE_CHOICE=60, FILL=0, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:23.694 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 2422 topics. IDs: [384948, 384949, 384950, 384951, 384952, 384953, 384954, 384955, 384956, 384957, 384958, 384959, 384960, 384961, 384962, 384963, 384964, 384965, 384966, 384967, 384968, 384969, 384970, 384971, 384972, 384973, 384974, 384975, 384976, 384977, 384978, 384979, 384980, 384981, 384982, 384983, 384984, 384985, 384986, 384987, 384988, 384989, 384990, 384991, 384992, 384993, 384994, 384995, 384996, 384997, 384998, 384999, 385000, 385001, 385002, 385003, 385004, 385005, 385006, 385007, 385008, 385009, 385010, 385011, 385012, 385013, 385014, 385015, 385016, 385017, 385018, 385019, 385020, 385021, 385022, 385023, 385024, 385025, 385026, 385027, 385028, 385029, 385030, 385031, 385032, 385033, 385034, 385035, 385036, 385037, 385038, 385039, 385040, 385041, 385042, 385043, 385044, 385045, 385046, 385047, 385048, 385049, 385050, 385051, 385052, 385053, 385054, 385055, 385056, 385057, 385058, 385059, 385060, 385061, 385062, 385063, 385064, 385065, 385066, 385067, 385068, 385069, 385070, 385071, 385072, 385073, 385074, 385075, 385076, 385077, 385078, 385079, 385080, 385081, 385082, 385083, 385084, 385085, 385086, 385087, 385088, 385089, 385090, 385091, 385092, 385093, 385094, 385095, 385096, 385097, 385098, 385099, 385100, 385101, 385102, 385103, 385104, 385105, 385106, 385107, 385108, 385109, 385110, 385111, 385112, 385113, 385114, 385115, 385116, 385117, 385118, 385119, 385120, 385121, 385122, 385123, 385124, 385125, 385126, 385127, 385128, 385129, 385130, 385131, 385132, 385133, 385134, 385135, 385136, 385137, 385138, 385139, 385140, 385141, 385142, 385143, 385144, 385145, 385146, 385147, 385148, 385149, 385150, 385151, 385152, 385153, 385154, 385155, 385156, 385157, 385158, 385159, 385160, 385161, 385162, 385163, 385164, 385165, 385166, 385167, 385168, 385169, 385170, 385171, 385172, 385173, 385174, 385175, 385176, 385177, 385178, 385179, 385180, 385181, 385182, 385183, 385184, 385185, 385186, 385187, 385188, 385189, 385190, 385191, 385192, 385193, 385194, 385195, 385196, 385197, 385198, 385199, 385200, 385201, 385202, 385203, 385204, 385205, 385206, 385207, 385208, 385209, 385210, 385211, 385212, 385213, 385214, 385215, 385216, 385217, 385218, 385219, 385220, 385221, 385222, 385223, 385224, 385225, 385226, 385227, 385228, 385229, 385230, 385231, 385232, 385233, 385234, 385235, 385236, 385237, 385238, 385239, 385240, 385241, 385242, 385243, 385244, 385245, 385246, 385247, 385248, 385249, 385250, 385251, 385252, 385253, 385254, 385255, 385256, 385257, 385258, 385259, 385260, 385261, 385262, 385263, 385264, 385265, 385266, 385267, 385268, 385269, 385270, 385271, 385272, 385273, 385274, 385275, 385276, 385277, 385278, 385279, 385280, 385281, 385282, 385283, 385284, 385285, 385286, 385287, 385288, 385289, 385290, 385291, 385292, 385293, 385294, 385295, 385296, 385297, 385298, 385299, 385300, 385301, 385302, 385303, 385304, 385305, 385306, 385307, 385308, 384514, 384515, 384516, 384517, 384518, 384519, 384520, 384521, 384522, 384523, 384524, 384525, 384526, 384527, 384528, 384529, 384530, 384531, 384532, 384533, 384534, 384535, 384536, 384537, 384538, 384539, 384540, 384541, 384542, 384543, 384544, 384545, 384546, 384547, 384548, 384549, 384550, 384551, 384552, 384553, 384554, 384555, 384556, 384557, 384558, 384559, 384560, 384561, 384562, 384563, 384564, 384565, 384566, 384567, 384568, 384569, 384570, 384571, 384572, 384573, 384574, 384575, 384576, 384577, 384578, 384579, 384580, 384581, 384582, 384583, 384584, 384585, 384586, 384587, 384588, 384589, 384590, 384591, 384592, 384593, 384594, 384595, 384596, 384597, 384598, 384599, 384600, 384601, 384602, 384603, 384604, 384605, 384606, 384607, 384608, 384609, 384610, 384611, 384612, 384613, 384614, 384615, 384616, 384617, 384618, 384619, 384620, 384621, 384622, 384623, 384624, 384625, 384626, 384627, 384628, 384629, 384630, 384631, 384632, 384633, 384634, 384635, 384636, 384637, 384638, 384639, 384640, 384641, 384642, 384643, 384644, 384645, 384646, 384647, 384648, 384649, 384650, 384651, 384652, 384653, 384654, 384655, 384656, 384657, 384658, 384659, 384660, 384661, 384662, 384663, 384664, 384665, 384666, 384667, 384668, 384669, 384670, 384671, 384672, 384673, 384674, 384675, 384676, 384677, 384678, 384679, 384680, 384681, 384682, 384683, 384684, 384685, 384686, 384687, 384688, 384689, 384690, 384691, 384692, 384693, 384694, 384695, 384696, 384697, 384698, 384699, 384700, 384701, 384702, 384703, 384704, 384705, 384706, 384707, 384708, 384709, 384710, 384711, 384712, 384713, 384714, 384715, 384716, 384717, 384718, 384719, 384720, 384721, 384722, 384723, 384724, 384725, 384726, 384727, 384728, 384729, 384730, 384731, 384732, 384733, 384734, 384735, 384736, 384737, 384738, 384739, 384740, 384741, 384742, 384743, 384744, 384745, 384746, 384747, 384748, 384749, 384750, 384751, 384752, 384753, 384754, 384755, 384756, 384757, 384758, 384759, 384760, 384761, 384762, 384763, 384764, 384765, 384766, 384767, 384768, 384769, 384770, 384771, 384772, 384773, 384774, 384775, 384776, 384777, 384778, 384779, 384780, 384781, 384782, 384783, 384784, 384785, 384786, 384787, 384788, 384789, 384790, 384791, 384792, 384793, 384794, 384795, 384796, 384797, 384798, 384799, 384800, 384801, 384802, 384803, 384804, 384805, 384806, 384807, 384808, 384809, 384810, 384811, 384812, 384813, 384814, 384815, 384816, 384817, 384818, 384819, 384820, 384821, 384822, 384823, 384824, 384825, 384826, 384827, 384828, 384829, 384830, 384831, 384832, 384833, 384834, 384835, 384836, 384837, 384838, 384839, 384840, 384841, 384842, 384843, 384844, 384845, 384846, 384847, 384848, 384849, 384850, 384851, 384852, 384853, 384854, 384855, 384856, 384857, 384858, 384859, 384860, 384861, 384862, 384863, 384864, 384865, 384866, 384867, 384868, 384869, 384870, 384871, 384872, 384873, 384874, 384875, 384876, 384877, 384878, 384879, 384880, 384881, 384882, 384883, 384884, 384885, 384886, 384887, 384888, 384889, 384890, 384891, 384892, 384893, 384894, 384895, 384896, 384897, 384898, 384899, 384900, 384901, 384902, 384903, 384904, 384905, 384906, 384907, 384908, 384909, 384910, 384911, 384912, 384913, 384914, 384915, 384916, 384917, 384918, 384919, 384920, 384921, 384922, 384923, 384924, 384925, 384926, 384927, 384928, 384929, 384930, 384931, 384932, 384933, 384934, 384935, 384936, 384937, 384938, 384939, 384940, 384941, 384942, 384943, 384944, 384945, 384946, 384947, 331479, 331480, 331481, 331482, 331483, 331484, 331485, 331486, 331487, 331488, 331489, 331490, 331491, 331492, 331493, 331494, 331495, 331496, 331497, 331498, 331499, 331500, 331501, 331502, 331503, 331504, 331505, 331506, 331507, 331508, 331509, 331510, 331511, 331512, 331513, 331514, 331515, 331516, 331517, 331518, 331519, 331520, 331521, 331522, 331523, 331524, 331525, 331526, 331527, 331528, 331529, 331530, 331531, 331532, 331533, 331534, 331535, 331536, 331537, 331538, 331539, 331540, 331541, 331542, 331543, 331544, 331545, 331546, 331547, 331548, 331549, 331550, 331551, 331552, 331553, 331554, 331555, 331556, 331557, 331558, 331559, 331560, 331561, 331562, 331563, 331564, 331565, 331566, 331567, 331568, 331569, 331570, 331571, 331572, 331573, 331574, 331575, 331576, 331577, 331578, 331579, 331580, 331581, 331582, 331583, 331584, 331585, 331586, 331587, 331588, 331589, 331590, 331591, 331592, 331593, 331594, 331595, 331596, 331597, 331598, 331599, 331600, 331601, 331602, 331603, 331604, 331605, 331606, 331607, 331608, 331609, 331610, 331611, 331612, 331613, 331614, 331615, 331616, 331617, 331618, 331619, 331620, 331621, 331622, 331623, 331624, 331625, 331626, 331627, 331628, 331629, 331630, 331631, 331632, 331633, 331634, 331635, 331636, 331637, 331638, 331639, 331640, 331641, 331642, 331643, 331644, 331645, 331646, 331647, 331648, 331649, 331650, 331651, 331652, 331653, 331654, 331655, 331656, 331657, 331658, 331659, 331660, 331661, 331662, 331663, 331664, 331665, 331666, 331667, 331668, 331669, 331670, 331671, 331672, 331673, 331674, 331675, 331676, 331677, 331678, 331679, 331680, 331681, 331682, 331683, 331684, 331685, 331686, 331687, 331688, 331689, 331690, 331691, 331692, 331693, 331694, 331695, 331696, 331697, 331698, 331699, 331700, 331701, 331702, 331703, 331704, 331705, 331706, 331707, 331708, 331709, 331710, 331711, 331712, 331713, 331714, 331715, 331716, 331717, 331718, 331719, 331720, 331721, 331722, 331723, 331724, 331725, 331726, 331727, 331728, 331729, 331730, 331731, 331732, 331733, 331734, 331735, 331736, 331737, 331738, 331739, 331740, 331741, 331742, 331743, 331744, 331745, 331746, 331747, 331748, 331749, 331750, 331751, 331752, 331753, 331754, 331755, 331756, 331757, 331758, 331759, 331760, 331761, 331762, 331763, 331764, 331765, 331766, 331767, 331768, 331769, 331770, 331771, 331772, 331773, 331774, 331775, 331776, 331777, 331778, 331779, 331780, 331781, 331782, 331783, 331784, 331785, 331786, 331787, 331788, 331789, 331790, 331791, 331792, 331793, 331794, 331795, 331796, 331797, 331798, 331799, 331800, 331801, 331802, 331803, 331804, 331805, 331806, 331807, 331808, 331809, 331810, 331811, 331812, 331813, 331814, 331815, 331816, 331817, 331818, 331819, 331820, 331821, 331822, 331823, 331824, 331825, 331826, 331827, 331828, 331829, 331830, 331831, 331832, 331833, 331834, 331835, 331836, 331837, 331838, 331839, 331840, 331841, 331842, 331843, 331844, 331845, 331846, 331847, 331848, 331849, 331850, 331851, 331852, 331853, 331854, 331855, 331856, 331857, 331858, 331859, 331860, 331861, 331862, 331863, 331864, 331865, 331866, 331867, 331868, 331869, 331870, 331871, 331872, 331873, 331874, 331875, 331876, 331877, 331878, 331879, 331880, 331881, 331882, 331883, 331884, 331885, 331886, 331887, 331888, 331889, 331890, 331891, 331892, 331893, 331894, 331895, 331896, 331897, 331898, 331899, 331900, 331901, 331902, 331903, 331904, 331905, 331906, 331907, 331908, 331909, 331910, 331911, 331912, 331913, 331914, 331915, 331916, 331917, 331918, 331919, 331920, 331921, 331922, 331923, 331924, 331925, 331926, 331927, 331928, 331929, 331930, 331931, 331932, 331933, 331934, 331935, 331936, 331937, 331938, 331939, 331940, 331941, 331942, 331943, 331944, 331945, 331946, 331947, 331948, 331949, 331950, 331951, 331952, 331953, 331954, 331955, 331956, 331957, 331958, 331959, 331960, 331961, 331962, 331963, 331964, 331965, 331966, 331967, 331968, 331969, 331970, 331971, 331972, 331973, 331974, 331975, 331976, 331977, 331978, 331979, 331980, 331981, 331982, 331983, 331984, 331985, 331986, 331987, 331988, 331989, 331990, 331991, 331992, 331993, 331994, 331995, 331996, 331997, 331998, 331999, 332000, 332001, 332002, 332003, 332004, 332005, 332006, 332007, 332008, 332009, 332010, 332011, 332012, 332013, 332014, 332015, 332016, 332017, 332018, 332019, 332020, 332021, 332022, 332023, 332024, 332025, 332026, 332027, 332028, 332029, 332030, 332031, 332032, 332033, 332034, 332035, 332036, 332037, 332038, 332039, 332040, 332041, 332042, 332043, 332044, 332045, 332046, 332047, 332048, 332049, 332050, 332051, 332052, 332053, 332054, 332055, 332056, 332057, 332058, 332059, 332060, 332061, 332062, 332063, 332064, 332065, 332066, 332067, 332068, 332069, 332070, 332071, 332072, 332073, 332074, 332075, 332076, 332077, 332078, 332079, 332080, 332081, 332082, 332083, 332084, 332085, 332086, 332087, 332088, 332089, 332090, 332091, 332092, 332093, 332094, 332095, 332096, 332097, 332098, 332099, 332100, 332101, 332102, 332103, 332104, 332105, 332106, 332107, 332108, 332109, 332110, 332111, 332112, 332113, 332114, 332115, 332116, 332117, 332118, 332119, 332120, 332121, 332122, 332123, 332124, 332125, 332126, 332127, 332128, 332129, 332130, 332131, 332132, 332133, 332134, 332135, 332136, 332137, 332138, 332139, 332140, 332141, 332142, 332143, 332144, 332145, 332146, 332147, 332148, 332149, 332150, 332151, 332152, 332153, 332154, 332155, 332156, 332157, 332158, 332159, 332160, 332161, 332162, 332163, 332164, 332165, 332166, 332167, 332168, 332169, 332170, 332171, 332172, 332173, 332174, 332175, 332176, 332177, 332178, 332179, 332180, 332181, 332182, 332183, 332184, 332185, 332186, 332187, 332188, 332189, 332190, 332191, 332192, 332193, 332194, 332195, 332196, 332197, 332198, 332199, 332200, 332201, 332202, 332203, 332204, 332205, 332206, 332207, 332208, 332209, 332210, 332211, 332212, 332213, 332214, 332215, 332216, 332217, 332218, 332219, 332220, 332221, 332222, 332223, 332224, 332225, 332226, 332227, 332228, 332229, 332230, 332231, 332232, 332233, 332234, 332235, 332236, 332237, 332238, 332239, 332240, 332241, 332242, 332243, 332244, 332245, 332246, 332247, 332248, 332249, 332250, 332251, 332252, 332253, 332254, 332255, 332256, 332257, 332258, 332259, 332260, 332261, 332262, 332263, 332264, 332265, 332266, 332267, 332268, 332269, 332270, 332271, 332272, 332273, 332274, 332275, 332276, 332277, 332278, 332279, 332280, 332281, 332282, 332283, 332284, 332285, 332286, 332287, 332288, 332289, 332290, 332291, 332292, 332293, 332294, 332295, 332296, 332297, 332298, 332299, 332300, 332301, 332302, 332303, 332304, 332305, 332306, 332307, 332308, 332309, 332310, 332311, 332312, 332313, 332314, 332315, 332316, 332317, 332318, 332319, 332320, 332321, 332322, 332323, 332324, 332325, 332326, 332327, 332328, 332329, 332330, 332331, 332332, 332333, 332334, 332335, 332336, 332337, 332338, 332339, 332340, 332341, 332342, 332343, 332344, 332345, 332346, 332347, 332348, 332349, 332350, 332351, 332352, 332353, 332354, 332355, 332356, 332357, 332358, 332359, 332360, 332361, 332362, 332363, 332364, 332365, 332366, 332367, 332368, 332369, 332370, 332371, 332372, 332373, 332374, 332375, 332376, 332377, 332378, 332379, 332380, 332381, 332382, 332383, 332384, 332385, 332386, 332387, 332388, 332389, 332390, 332391, 332392, 332393, 332394, 332395, 332396, 332397, 332398, 332399, 332400, 332401, 332402, 332403, 332404, 332405, 332406, 332407, 332408, 332409, 332410, 332411, 332412, 332413, 332414, 332415, 332416, 332417, 332418, 332419, 332420, 332421, 332422, 332423, 332424, 332425, 332426, 332427, 332428, 332429, 332430, 332431, 332432, 332433, 332434, 332435, 332436, 332437, 332438, 332439, 332440, 332441, 332442, 332443, 332444, 332445, 332446, 332447, 332448, 332449, 332450, 332451, 332452, 332453, 332454, 332455, 332456, 332457, 332458, 332459, 332460, 332461, 332462, 332463, 332464, 332465, 332466, 332467, 332468, 332469, 332470, 332471, 332472, 332473, 332474, 332475, 332476, 332477, 332478, 332479, 332480, 332481, 332482, 332483, 332484, 332485, 332486, 332487, 332488, 332489, 332490, 332491, 332492, 332493, 332494, 332495, 332496, 332497, 332498, 332499, 332500, 332501, 332502, 332503, 332504, 332505, 332506, 332507, 332508, 332509, 332510, 332511, 332512, 332513, 332514, 332515, 332516, 332517, 332518, 332519, 332520, 332521, 332522, 332523, 332524, 332525, 332526, 332527, 332528, 332529, 332530, 332531, 332532, 332533, 332534, 332535, 332536, 332537, 332538, 332539, 332540, 332541, 332542, 332543, 332544, 332545, 332546, 332547, 332548, 332549, 332550, 332551, 332552, 332553, 332554, 332555, 332556, 332557, 332558, 332559, 332560, 332561, 332562, 332563, 332564, 332565, 332566, 332567, 332568, 332569, 332570, 332571, 332572, 332573, 332574, 332575, 332576, 332577, 332578, 332579, 332580, 332581, 332582, 332583, 332584, 332585, 332586, 332587, 332588, 332589, 332590, 332591, 332592, 332593, 332594, 332595, 332596, 332597, 332598, 332599, 332600, 332601, 332602, 332603, 332604, 332605, 332606, 332607, 332608, 332609, 332610, 332611, 332612, 332613, 332614, 332615, 332616, 332617, 332618, 332619, 332620, 332621, 332622, 332623, 332624, 332625, 332626, 332627, 332628, 332629, 332630, 332631, 332632, 332633, 332634, 332635, 332636, 332637, 332638, 332639, 332640, 332641, 332642, 332643, 332644, 332645, 332646, 332647, 332648, 332649, 332650, 332651, 332652, 332653, 332654, 332655, 332656, 332657, 332658, 332659, 332660, 332661, 332662, 332663, 332664, 332665, 332666, 332667, 332668, 332669, 332670, 332671, 332672, 332673, 332674, 332675, 332676, 332677, 332678, 332679, 332680, 332681, 332682, 332683, 332684, 332685, 332686, 332687, 332688, 332689, 332690, 332691, 332692, 332693, 332694, 332695, 332696, 332697, 332698, 332699, 332700, 332701, 332702, 332703, 332704, 332705, 332706, 332707, 332708, 332709, 332710, 332711, 332712, 332713, 332714, 332715, 332716, 332717, 332718, 332719, 332720, 332721, 332722, 332723, 332724, 332725, 332726, 332727, 332728, 332729, 332730, 332731, 332732, 332733, 332734, 332735, 332736, 332737, 332738, 332739, 332740, 332741, 332742, 332743, 332744, 332745, 332746, 332747, 332748, 332749, 332750, 332751, 332752, 332753, 332754, 332755, 332756, 332757, 332758, 332759, 332760, 332761, 332762, 332763, 332764, 332765, 332766, 332767, 332768, 332769, 332770, 332771, 332772, 332773, 332774, 332775, 332776, 332777, 332778, 332779, 332780, 332781, 332782, 332783, 332784, 332785, 332786, 332787, 332788, 332789, 332790, 332791, 332792, 332793, 332794, 332795, 332796, 332797, 332798, 332799, 332800, 332801, 332802, 332803, 332804, 332805, 332806, 332807, 332808, 332809, 332810, 332811, 332812, 332813, 332814, 332815, 332816, 332817, 332818, 332819, 332820, 332821, 332822, 332823, 332824, 332825, 332826, 332827, 332828, 332829, 332830, 332831, 332832, 332833, 332834, 332835, 332836, 332837, 332838, 332839, 332840, 332841, 332842, 332843, 332844, 332845, 332846, 332847, 332848, 332849, 332850, 332851, 332852, 332853, 332854, 332855, 332856, 332857, 332858, 332859, 332860, 332861, 332862, 332863, 332864, 332865, 332866, 332867, 332868, 332869, 332870, 332871, 332872, 332873, 332874, 332875, 332876, 332877, 332878, 332879, 332880, 332881, 332882, 332883, 332884, 332885, 332886, 332887, 332888, 332889, 332890, 332891, 332892, 332893, 332894, 332895, 332896, 332897, 332898, 332899, 332900, 332901, 332902, 332903, 332904, 332905, 332906, 332907, 332908, 332909, 332910, 332911, 332912, 332913, 332914, 332915, 332916, 332917, 332918, 332919, 332920, 332921, 332922, 332923, 332924, 332925, 332926, 332927, 332928, 332929, 332930, 332931, 332932, 332933, 332934, 332935, 332936, 332937, 332938, 332939, 332940, 332941, 332942, 332943, 332944, 332945, 332946, 332947, 332948, 332949, 332950, 332951, 332952, 332953, 332954, 332955, 332956, 332957, 332958, 332959, 332960, 332961, 332962, 332963, 332964, 332965, 332966, 332967, 332968, 332969, 332970, 332971, 337708, 337709, 337710, 337711, 337712, 337713, 337714, 337715, 337716, 337717, 337718, 337719, 337720, 337721, 337722, 337723, 337724, 337725, 337726, 337727, 337728, 337729, 337730, 337731, 337732, 337733, 337734, 337735, 337736, 337737, 337738, 337739, 337740, 337741, 337742, 337743, 337744, 337745, 337746, 337747, 337748, 337749, 337750, 337751, 337752, 337753, 337754, 337755, 337756, 337757, 337758, 337759, 337760, 337761, 337762, 337763, 337764, 337765, 337766, 337767, 337768, 337769, 337770, 337771, 337772, 337773, 337774, 337775, 337776, 337777, 337778, 337779, 337780, 337781, 337782, 337783, 337784, 337785, 337786, 337787, 337788, 337789, 337790, 337791, 337792, 337793, 337794, 337795, 337796, 337797, 337798, 337799, 337800, 337801, 337802, 337803, 337804, 337805, 337806, 337807, 337808, 337809, 337810, 337811, 337812, 337813, 337814, 337815, 337816, 337817, 337818, 337819, 337820, 337821, 337822, 337823, 337824, 337825, 337826, 337827, 337828, 337829, 337830, 337831, 337832, 337833, 337834, 337835, 337836, 337837, 337838, 337839, 337840, 337841]
2025-07-27 15:16:23.697 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting type-aware smart filtering. Initial total topics: 2422. Required type counts from request: {multiple=20, short=0, judge=20, choice=60, fill=0}
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Initial topic distribution by type (from input list):
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'multiple': 421 topics
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'judge': 602 topics
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'choice': 1397 topics
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics (present in input, but not in requiredTypeCounts or map is null)
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'short': 0 topics in input (but 0 required)
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'fill': 0 topics in input (but 0 required)
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'multiple' - required: 20, available in input: 421
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 421 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.698 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 48 topics remaining after reuse interval filtering (out of 62 initial topics).
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.698 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.698 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 81 topics remaining after reuse interval filtering (out of 81 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 128 topics remaining after reuse interval filtering (out of 138 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 124 topics remaining after reuse interval filtering (out of 140 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 198 topics remaining (from 421 initial).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'multiple' contributed 198 topics after smartFilterForType. Total in result now: 198
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'judge' - required: 20, available in input: 602
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 602 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 59 topics remaining after reuse interval filtering (out of 63 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 81 topics remaining after reuse interval filtering (out of 104 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 212 topics remaining after reuse interval filtering (out of 220 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 210 topics remaining after reuse interval filtering (out of 215 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 602 initial).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'judge' contributed 200 topics after smartFilterForType. Total in result now: 398
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'choice' - required: 60, available in input: 1397
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 1397 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 4 knowledge points
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 222 topics remaining after reuse interval filtering (out of 236 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 220 topics remaining after reuse interval filtering (out of 249 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 406 topics remaining after reuse interval filtering (out of 461 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T15:16:23.699 will be kept. Min reuse interval: 1 days.
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 429 topics remaining after reuse interval filtering (out of 451 initial topics).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 200 topics remaining (from 1397 initial).
2025-07-27 15:16:23.699 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'choice' contributed 200 topics after smartFilterForType. Total in result now: 598
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type-aware filtering completed. Final total topics selected: 598. Initial total topics was: 2422
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 3: After Type-Aware Smart Diversity Filter (with KP-level control), 598 topics available for GA (input size was 2422). MinReuseIntervalDays: null
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Diversity filtering already applied via smartFilter. 598 topics remain.
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4a: Checking for precise allocation requirements...
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Checking knowledge point configurations for short answer requirements...
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 602: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 603: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 269: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 270: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Overall hasShortAnswerConfiguration: false
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4b: Executing genetic algorithm for basic types with 598 candidate topics...
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA will handle basic types only: {multiple=20, judge=20, choice=60, fill=0}
2025-07-27 15:16:23.701 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA target score: 300 (total 300 - short answer 0)
2025-07-27 15:16:23.706 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Created execution record with ID: 13
2025-07-27 15:16:23.711 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA started - Population: 300, Generations: 200, Questions: 598
2025-07-27 15:16:23.810 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generated 90 greedy seed chromosomes
2025-07-27 15:16:23.814 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized enhanced population with 300 chromosomes
2025-07-27 15:16:23.814 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Intelligent population initialized: 300 total chromosomes
2025-07-27 15:16:23.933 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 1: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.48998852758329914ms, NoImprove=0.483973773284148, Elite=0.5281478632298943
2025-07-27 15:16:24.123 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 2: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49205347441804ms, NoImprove=0.48572677271325165, Elite=0.5118344408343679
2025-07-27 15:16:24.329 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 3: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4934094044540925ms, NoImprove=0.4872118618344708, Elite=0.5085910620970254
2025-07-27 15:16:24.548 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 4: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.496105420709749ms, NoImprove=0.48839725052655136, Elite=0.4921476391801891
2025-07-27 15:16:24.734 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 5: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4995860449618128ms, NoImprove=0.4895095303946075, Elite=0.4789800965896777
2025-07-27 15:16:25.593 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 10: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4996936116144291ms, NoImprove=0.48562441801197603, Elite=0.020404434155261875
2025-07-27 15:16:26.719 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 20: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5025105835846295ms, NoImprove=0.4985313831467183, Elite=0.12192216051091619
2025-07-27 15:16:27.749 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 30: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5036836979707424ms, NoImprove=0.5008006234124153, Elite=0.1540400512965366
2025-07-27 15:16:28.776 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 40: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5044022209579724ms, NoImprove=0.49495351508991964, Elite=0.0414874444661381
2025-07-27 15:16:29.784 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 50: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5051189451304897ms, NoImprove=0.49417748235660836, Elite=0.08958246697721528
2025-07-27 15:16:30.822 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 60: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5059474626787261ms, NoImprove=0.503151393123972, Elite=0.1134583639013487
2025-07-27 15:16:32.023 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 70: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5068895780330621ms, NoImprove=0.49629046057244003, Elite=0.06525393447768246
2025-07-27 15:16:33.622 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 80: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5073427674558159ms, NoImprove=0.49705218938540024, Elite=0.04366379442484335
2025-07-27 15:16:34.677 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 90: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.50774065149597ms, NoImprove=0.4981688372597528, Elite=0.01932646536196822
2025-07-27 15:16:35.718 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 100: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5079794568124104ms, NoImprove=0.49776617415755414, Elite=0.050142711479387106
2025-07-27 15:16:36.810 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 110: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.5082520906220698ms, NoImprove=0.49827206657564754, Elite=0.024147381067680992
2025-07-27 15:16:36.957 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination at generation 111
2025-07-27 15:16:36.957 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA completed in 13246ms. Best fitness: {:.4f}, Selected: 0.5082520906220698 topics
2025-07-27 15:16:36.957 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Algorithm performance metrics: {avg_diversity=0.09918675001034787, convergence_rate=3.391439768725272E-4, total_time_ms=13246, generations=111, final_diversity=0.02934023781275349}
2025-07-27 15:16:36.970 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined results: 100 basic type topics + 0 short answer topics = 100 total topics
2025-07-27 15:16:36.970 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined algorithm selected 100 topics with type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:36.970 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined result: All type constraints met!
2025-07-27 15:16:36.970 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 300, 同时保持题型分布
2025-07-27 15:16:36.970 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {multiple=20, judge=20, choice=60}
2025-07-27 15:16:36.971 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:36.971 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:36.971 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 100 topics
2025-07-27 15:16:36.971 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:36.971 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - After DP adjustment: Type distribution = {multiple=20, judge=20, choice=60}
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP score adjustment complete. Final total score = 300, with type distribution preserved.
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP adjustment completed in 1 ms
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={multiple=20, judge=20, choice=60}, 目标={SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 各题型题目数量满足要求，无需调整
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:36.971 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 2422 topics
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final DP adjustment succeeded. totalScore=300, typeCounts={singleChoice=60, judgment=20, multipleChoice=20}.
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 100 topics.
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 100. Requested global counts (for warning reference): {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:36.972 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:36.974 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:36.974 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:36.983 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:36.985 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:36.985 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:36.985 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 300) after enforcing type counts...
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - DPAdjuster will receive targetTypeCounts (normalized keys): {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=60, judgment=20, multipleChoice=20}, Target type counts: {singleChoice=60, fillBlank=0, judgment=20, multipleChoice=20, shortAnswer=0}
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 15:16:36.987 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 15:16:36.988 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 15:16:36.988 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 15:16:36.990 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:36.990 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20, SHORT=0}
2025-07-27 15:16:37.000 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求60/实际60
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求20/实际20
- SHORT: 请求0/实际0
2025-07-27 15:16:37.001 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 60 topics of non-standard type 'SINGLE_CHOICE' (requested 60)
2025-07-27 15:16:37.001 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 15:16:37.001 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'MULTIPLE_CHOICE' (requested 20)
2025-07-27 15:16:37.003 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}
2025-07-27 15:16:37.003 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - After final enforcement: 100 topics, calculated score: 300 (target score: 300)
2025-07-27 15:16:37.005 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Final type counts: {SINGLE_CHOICE=60, JUDGE=20, MULTIPLE_CHOICE=20}, Requested counts: {SINGLE_CHOICE=60, MULTIPLE_CHOICE=20, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 15:16:37.056 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 309. Title: 测试试卷 (第3套)
2025-07-27 15:16:37.173 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 100 topics.
2025-07-27 15:16:37.173 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.engine.KnowledgePointUsageTracker - KnowledgePointUsageTracker: Updating usage stats for 4 knowledge points
2025-07-27 15:16:37.289 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 第 3 套试卷生成成功: 测试试卷 (第3套)
2025-07-27 15:16:37.289 [http-nio-8081-exec-8] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 批量生成完成: 总计 3 套, 成功 3 套, 失败 0 套
2025-07-27 15:16:38.575 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 15:16:38.583 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=44, pages=9, current=1, size=5, records=5
2025-07-27 15:16:39.805 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loading 100 topics for paper id: 309
2025-07-27 15:16:39.816 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Found 100 topics in database (from 100 requested IDs) for paper id: 309
2025-07-27 15:16:39.822 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Returning 100 ordered topics for paper id: 309
2025-07-27 15:16:39.823 [http-nio-8081-exec-2] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loaded and mapped difficulty distribution from paper: {hard=0.1, medium=0.7, easy=0.2}
2025-07-27 15:16:44.439 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loading 100 topics for paper id: 307
2025-07-27 15:16:44.447 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Found 100 topics in database (from 100 requested IDs) for paper id: 307
2025-07-27 15:16:44.450 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Returning 100 ordered topics for paper id: 307
2025-07-27 15:16:44.450 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loaded and mapped difficulty distribution from paper: {hard=0.1, medium=0.7, easy=0.2}
2025-07-27 15:31:46.868 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 15:31:46.970 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 15:31:46.970 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 15:31:46.990 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 15:47:24.135 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 15:47:24.220 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 15:47:24.220 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 15:47:24.240 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 15:54:08.695 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 15:54:08.800 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 15:54:08.800 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 15:54:08.820 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 16:01:11.034 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 16:01:11.138 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 16:01:11.138 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 16:01:11.159 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 16:32:25.479 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 16:32:25.630 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 16:32:25.631 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 16:32:25.667 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 16:33:40.966 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 16:33:41.107 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 16:33:41.108 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 16:33:41.137 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
2025-07-27 16:33:55.596 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 16:33:55.703 [http-nio-8081-exec-3] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=44, pages=9, current=1, size=5, records=5
2025-07-27 16:34:29.447 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='信息技术测试试卷', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=239, knowledgeName=信息技术选修二_网络基础, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=801, knowledgeName=信息技术必修1数据与计算, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=802, knowledgeName=信息技术必修2信息系统与社会, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=803, knowledgeName=信息技术必修1数据与计算（科教版）, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=804, knowledgeName=信息技术必修2信息系统与社会（教科版）, questionCount=20, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=30, MULTIPLE_CHOICE=30, JUDGE=40, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={easy=0.3, medium=0.5, hard=0.2}
2025-07-27 16:34:29.449 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(paperId=null, knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=239, knowledgeName=信息技术选修二_网络基础, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=801, knowledgeName=信息技术必修1数据与计算, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=802, knowledgeName=信息技术必修2信息系统与社会, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=803, knowledgeName=信息技术必修1数据与计算（科教版）, questionCount=20, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=804, knowledgeName=信息技术必修2信息系统与社会（教科版）, questionCount=20, includeShortAnswer=false, shortAnswerCount=0)], title=信息技术测试试卷, totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=30, MULTIPLE_CHOICE=30, JUDGE=40, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-07-27 16:34:29.453 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Converted type counts for entire process: frontend={SINGLE_CHOICE=30, FILL=0, JUDGE=40, MULTIPLE_CHOICE=30, SHORT=0} -> database={multiple=30, short=0, judge=40, choice=30, fill=0}
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - No topics selected after processing KnowledgePointConfigs. Cannot proceed.
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 0. Requested global counts (for warning reference): {SINGLE_CHOICE=30, MULTIPLE_CHOICE=30, JUDGE=40, FILL=0, SHORT=0}
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 300) after enforcing type counts...
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - DPAdjuster will receive targetTypeCounts (normalized keys): {singleChoice=30, fillBlank=0, judgment=40, multipleChoice=30, shortAnswer=0}
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Input topics list is null or empty. Returning empty list.
2025-07-27 16:34:29.460 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts
2025-07-27 16:34:29.461 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Bypassing due to null/empty original topics. Original size: 0
2025-07-27 16:34:29.461 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - After final enforcement: 0 topics, calculated score: 300 (target score: 300)
2025-07-27 16:34:29.462 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Final type counts: {}, Requested counts: {SINGLE_CHOICE=30, MULTIPLE_CHOICE=30, JUDGE=40, FILL=0, SHORT=0}
2025-07-27 16:34:29.462 [http-nio-8081-exec-7] WARN  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - No topics selected or enforced for paper generation. Title: 信息技术测试试卷
2025-07-27 16:34:30.231 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 16:34:30.238 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=44, pages=9, current=1, size=5, records=5
2025-07-27 16:34:37.152 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 16:34:37.159 [http-nio-8081-exec-6] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=44, pages=9, current=1, size=5, records=5
2025-07-27 16:35:08.664 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Generating paper with detailed request: Title='信息技术测试', KnowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=22, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=27, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=51, includeShortAnswer=false, shortAnswerCount=0)], GlobalTypeCounts={SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}, GlobalTypeScores={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, DifficultyCriteria={easy=0.3, medium=0.5, hard=0.2}
2025-07-27 16:35:08.664 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Starting paper generation process with detailed request: PaperGenerationRequest(paperId=null, knowledgePointConfigs=[KnowledgePointConfigRequest(knowledgeId=602, knowledgeName=通用技术必修技术与设计1（人教版）, questionCount=22, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=603, knowledgeName=通用技术必修技术与设计2（人教版）, questionCount=27, includeShortAnswer=false, shortAnswerCount=0), KnowledgePointConfigRequest(knowledgeId=269, knowledgeName=心理健康高中一年级, questionCount=51, includeShortAnswer=false, shortAnswerCount=0)], title=信息技术测试, totalScore=300, typeScoreMap={SINGLE_CHOICE=3, MULTIPLE_CHOICE=3, JUDGE=3, FILL=3, SHORT=10}, difficultyCriteria={easy=0.3, medium=0.5, hard=0.2}, topicTypeCounts={SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}, minReuseIntervalDays=null)
2025-07-27 16:35:08.665 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Converted type counts for entire process: frontend={SINGLE_CHOICE=40, FILL=0, JUDGE=20, MULTIPLE_CHOICE=40, SHORT=0} -> database={multiple=40, short=0, judge=20, choice=40, fill=0}
2025-07-27 16:35:08.692 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 602 contributed 361 questions
2025-07-27 16:35:08.717 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 603 contributed 434 questions
2025-07-27 16:35:08.755 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Knowledge point 269 contributed 821 questions
2025-07-27 16:35:08.765 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 当前题目池已满足所有题型要求，无需扩展
2025-07-27 16:35:08.765 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final topic pool size after potential expansion: 1616. Target global counts: {SINGLE_CHOICE=40, FILL=0, JUDGE=20, MULTIPLE_CHOICE=40, SHORT=0}
2025-07-27 16:35:08.802 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Candidate pool for GA (from allTopicsFromReferencedKps, pre-diversity filter): 1616 topics. IDs: [384948, 384949, 384950, 384951, 384952, 384953, 384954, 384955, 384956, 384957, 384958, 384959, 384960, 384961, 384962, 384963, 384964, 384965, 384966, 384967, 384968, 384969, 384970, 384971, 384972, 384973, 384974, 384975, 384976, 384977, 384978, 384979, 384980, 384981, 384982, 384983, 384984, 384985, 384986, 384987, 384988, 384989, 384990, 384991, 384992, 384993, 384994, 384995, 384996, 384997, 384998, 384999, 385000, 385001, 385002, 385003, 385004, 385005, 385006, 385007, 385008, 385009, 385010, 385011, 385012, 385013, 385014, 385015, 385016, 385017, 385018, 385019, 385020, 385021, 385022, 385023, 385024, 385025, 385026, 385027, 385028, 385029, 385030, 385031, 385032, 385033, 385034, 385035, 385036, 385037, 385038, 385039, 385040, 385041, 385042, 385043, 385044, 385045, 385046, 385047, 385048, 385049, 385050, 385051, 385052, 385053, 385054, 385055, 385056, 385057, 385058, 385059, 385060, 385061, 385062, 385063, 385064, 385065, 385066, 385067, 385068, 385069, 385070, 385071, 385072, 385073, 385074, 385075, 385076, 385077, 385078, 385079, 385080, 385081, 385082, 385083, 385084, 385085, 385086, 385087, 385088, 385089, 385090, 385091, 385092, 385093, 385094, 385095, 385096, 385097, 385098, 385099, 385100, 385101, 385102, 385103, 385104, 385105, 385106, 385107, 385108, 385109, 385110, 385111, 385112, 385113, 385114, 385115, 385116, 385117, 385118, 385119, 385120, 385121, 385122, 385123, 385124, 385125, 385126, 385127, 385128, 385129, 385130, 385131, 385132, 385133, 385134, 385135, 385136, 385137, 385138, 385139, 385140, 385141, 385142, 385143, 385144, 385145, 385146, 385147, 385148, 385149, 385150, 385151, 385152, 385153, 385154, 385155, 385156, 385157, 385158, 385159, 385160, 385161, 385162, 385163, 385164, 385165, 385166, 385167, 385168, 385169, 385170, 385171, 385172, 385173, 385174, 385175, 385176, 385177, 385178, 385179, 385180, 385181, 385182, 385183, 385184, 385185, 385186, 385187, 385188, 385189, 385190, 385191, 385192, 385193, 385194, 385195, 385196, 385197, 385198, 385199, 385200, 385201, 385202, 385203, 385204, 385205, 385206, 385207, 385208, 385209, 385210, 385211, 385212, 385213, 385214, 385215, 385216, 385217, 385218, 385219, 385220, 385221, 385222, 385223, 385224, 385225, 385226, 385227, 385228, 385229, 385230, 385231, 385232, 385233, 385234, 385235, 385236, 385237, 385238, 385239, 385240, 385241, 385242, 385243, 385244, 385245, 385246, 385247, 385248, 385249, 385250, 385251, 385252, 385253, 385254, 385255, 385256, 385257, 385258, 385259, 385260, 385261, 385262, 385263, 385264, 385265, 385266, 385267, 385268, 385269, 385270, 385271, 385272, 385273, 385274, 385275, 385276, 385277, 385278, 385279, 385280, 385281, 385282, 385283, 385284, 385285, 385286, 385287, 385288, 385289, 385290, 385291, 385292, 385293, 385294, 385295, 385296, 385297, 385298, 385299, 385300, 385301, 385302, 385303, 385304, 385305, 385306, 385307, 385308, 384514, 384515, 384516, 384517, 384518, 384519, 384520, 384521, 384522, 384523, 384524, 384525, 384526, 384527, 384528, 384529, 384530, 384531, 384532, 384533, 384534, 384535, 384536, 384537, 384538, 384539, 384540, 384541, 384542, 384543, 384544, 384545, 384546, 384547, 384548, 384549, 384550, 384551, 384552, 384553, 384554, 384555, 384556, 384557, 384558, 384559, 384560, 384561, 384562, 384563, 384564, 384565, 384566, 384567, 384568, 384569, 384570, 384571, 384572, 384573, 384574, 384575, 384576, 384577, 384578, 384579, 384580, 384581, 384582, 384583, 384584, 384585, 384586, 384587, 384588, 384589, 384590, 384591, 384592, 384593, 384594, 384595, 384596, 384597, 384598, 384599, 384600, 384601, 384602, 384603, 384604, 384605, 384606, 384607, 384608, 384609, 384610, 384611, 384612, 384613, 384614, 384615, 384616, 384617, 384618, 384619, 384620, 384621, 384622, 384623, 384624, 384625, 384626, 384627, 384628, 384629, 384630, 384631, 384632, 384633, 384634, 384635, 384636, 384637, 384638, 384639, 384640, 384641, 384642, 384643, 384644, 384645, 384646, 384647, 384648, 384649, 384650, 384651, 384652, 384653, 384654, 384655, 384656, 384657, 384658, 384659, 384660, 384661, 384662, 384663, 384664, 384665, 384666, 384667, 384668, 384669, 384670, 384671, 384672, 384673, 384674, 384675, 384676, 384677, 384678, 384679, 384680, 384681, 384682, 384683, 384684, 384685, 384686, 384687, 384688, 384689, 384690, 384691, 384692, 384693, 384694, 384695, 384696, 384697, 384698, 384699, 384700, 384701, 384702, 384703, 384704, 384705, 384706, 384707, 384708, 384709, 384710, 384711, 384712, 384713, 384714, 384715, 384716, 384717, 384718, 384719, 384720, 384721, 384722, 384723, 384724, 384725, 384726, 384727, 384728, 384729, 384730, 384731, 384732, 384733, 384734, 384735, 384736, 384737, 384738, 384739, 384740, 384741, 384742, 384743, 384744, 384745, 384746, 384747, 384748, 384749, 384750, 384751, 384752, 384753, 384754, 384755, 384756, 384757, 384758, 384759, 384760, 384761, 384762, 384763, 384764, 384765, 384766, 384767, 384768, 384769, 384770, 384771, 384772, 384773, 384774, 384775, 384776, 384777, 384778, 384779, 384780, 384781, 384782, 384783, 384784, 384785, 384786, 384787, 384788, 384789, 384790, 384791, 384792, 384793, 384794, 384795, 384796, 384797, 384798, 384799, 384800, 384801, 384802, 384803, 384804, 384805, 384806, 384807, 384808, 384809, 384810, 384811, 384812, 384813, 384814, 384815, 384816, 384817, 384818, 384819, 384820, 384821, 384822, 384823, 384824, 384825, 384826, 384827, 384828, 384829, 384830, 384831, 384832, 384833, 384834, 384835, 384836, 384837, 384838, 384839, 384840, 384841, 384842, 384843, 384844, 384845, 384846, 384847, 384848, 384849, 384850, 384851, 384852, 384853, 384854, 384855, 384856, 384857, 384858, 384859, 384860, 384861, 384862, 384863, 384864, 384865, 384866, 384867, 384868, 384869, 384870, 384871, 384872, 384873, 384874, 384875, 384876, 384877, 384878, 384879, 384880, 384881, 384882, 384883, 384884, 384885, 384886, 384887, 384888, 384889, 384890, 384891, 384892, 384893, 384894, 384895, 384896, 384897, 384898, 384899, 384900, 384901, 384902, 384903, 384904, 384905, 384906, 384907, 384908, 384909, 384910, 384911, 384912, 384913, 384914, 384915, 384916, 384917, 384918, 384919, 384920, 384921, 384922, 384923, 384924, 384925, 384926, 384927, 384928, 384929, 384930, 384931, 384932, 384933, 384934, 384935, 384936, 384937, 384938, 384939, 384940, 384941, 384942, 384943, 384944, 384945, 384946, 384947, 331479, 331480, 331481, 331482, 331483, 331484, 331485, 331486, 331487, 331488, 331489, 331490, 331491, 331492, 331493, 331494, 331495, 331496, 331497, 331498, 331499, 331500, 331501, 331502, 331503, 331504, 331505, 331506, 331507, 331508, 331509, 331510, 331511, 331512, 331513, 331514, 331515, 331516, 331517, 331518, 331519, 331520, 331521, 331522, 331523, 331524, 331525, 331526, 331527, 331528, 331529, 331530, 331531, 331532, 331533, 331534, 331535, 331536, 331537, 331538, 331539, 331540, 331541, 331542, 331543, 331544, 331545, 331546, 331547, 331548, 331549, 331550, 331551, 331552, 331553, 331554, 331555, 331556, 331557, 331558, 331559, 331560, 331561, 331562, 331563, 331564, 331565, 331566, 331567, 331568, 331569, 331570, 331571, 331572, 331573, 331574, 331575, 331576, 331577, 331578, 331579, 331580, 331581, 331582, 331583, 331584, 331585, 331586, 331587, 331588, 331589, 331590, 331591, 331592, 331593, 331594, 331595, 331596, 331597, 331598, 331599, 331600, 331601, 331602, 331603, 331604, 331605, 331606, 331607, 331608, 331609, 331610, 331611, 331612, 331613, 331614, 331615, 331616, 331617, 331618, 331619, 331620, 331621, 331622, 331623, 331624, 331625, 331626, 331627, 331628, 331629, 331630, 331631, 331632, 331633, 331634, 331635, 331636, 331637, 331638, 331639, 331640, 331641, 331642, 331643, 331644, 331645, 331646, 331647, 331648, 331649, 331650, 331651, 331652, 331653, 331654, 331655, 331656, 331657, 331658, 331659, 331660, 331661, 331662, 331663, 331664, 331665, 331666, 331667, 331668, 331669, 331670, 331671, 331672, 331673, 331674, 331675, 331676, 331677, 331678, 331679, 331680, 331681, 331682, 331683, 331684, 331685, 331686, 331687, 331688, 331689, 331690, 331691, 331692, 331693, 331694, 331695, 331696, 331697, 331698, 331699, 331700, 331701, 331702, 331703, 331704, 331705, 331706, 331707, 331708, 331709, 331710, 331711, 331712, 331713, 331714, 331715, 331716, 331717, 331718, 331719, 331720, 331721, 331722, 331723, 331724, 331725, 331726, 331727, 331728, 331729, 331730, 331731, 331732, 331733, 331734, 331735, 331736, 331737, 331738, 331739, 331740, 331741, 331742, 331743, 331744, 331745, 331746, 331747, 331748, 331749, 331750, 331751, 331752, 331753, 331754, 331755, 331756, 331757, 331758, 331759, 331760, 331761, 331762, 331763, 331764, 331765, 331766, 331767, 331768, 331769, 331770, 331771, 331772, 331773, 331774, 331775, 331776, 331777, 331778, 331779, 331780, 331781, 331782, 331783, 331784, 331785, 331786, 331787, 331788, 331789, 331790, 331791, 331792, 331793, 331794, 331795, 331796, 331797, 331798, 331799, 331800, 331801, 331802, 331803, 331804, 331805, 331806, 331807, 331808, 331809, 331810, 331811, 331812, 331813, 331814, 331815, 331816, 331817, 331818, 331819, 331820, 331821, 331822, 331823, 331824, 331825, 331826, 331827, 331828, 331829, 331830, 331831, 331832, 331833, 331834, 331835, 331836, 331837, 331838, 331839, 331840, 331841, 331842, 331843, 331844, 331845, 331846, 331847, 331848, 331849, 331850, 331851, 331852, 331853, 331854, 331855, 331856, 331857, 331858, 331859, 331860, 331861, 331862, 331863, 331864, 331865, 331866, 331867, 331868, 331869, 331870, 331871, 331872, 331873, 331874, 331875, 331876, 331877, 331878, 331879, 331880, 331881, 331882, 331883, 331884, 331885, 331886, 331887, 331888, 331889, 331890, 331891, 331892, 331893, 331894, 331895, 331896, 331897, 331898, 331899, 331900, 331901, 331902, 331903, 331904, 331905, 331906, 331907, 331908, 331909, 331910, 331911, 331912, 331913, 331914, 331915, 331916, 331917, 331918, 331919, 331920, 331921, 331922, 331923, 331924, 331925, 331926, 331927, 331928, 331929, 331930, 331931, 331932, 331933, 331934, 331935, 331936, 331937, 331938, 331939, 331940, 331941, 331942, 331943, 331944, 331945, 331946, 331947, 331948, 331949, 331950, 331951, 331952, 331953, 331954, 331955, 331956, 331957, 331958, 331959, 331960, 331961, 331962, 331963, 331964, 331965, 331966, 331967, 331968, 331969, 331970, 331971, 331972, 331973, 331974, 331975, 331976, 331977, 331978, 331979, 331980, 331981, 331982, 331983, 331984, 331985, 331986, 331987, 331988, 331989, 331990, 331991, 331992, 331993, 331994, 331995, 331996, 331997, 331998, 331999, 332000, 332001, 332002, 332003, 332004, 332005, 332006, 332007, 332008, 332009, 332010, 332011, 332012, 332013, 332014, 332015, 332016, 332017, 332018, 332019, 332020, 332021, 332022, 332023, 332024, 332025, 332026, 332027, 332028, 332029, 332030, 332031, 332032, 332033, 332034, 332035, 332036, 332037, 332038, 332039, 332040, 332041, 332042, 332043, 332044, 332045, 332046, 332047, 332048, 332049, 332050, 332051, 332052, 332053, 332054, 332055, 332056, 332057, 332058, 332059, 332060, 332061, 332062, 332063, 332064, 332065, 332066, 332067, 332068, 332069, 332070, 332071, 332072, 332073, 332074, 332075, 332076, 332077, 332078, 332079, 332080, 332081, 332082, 332083, 332084, 332085, 332086, 332087, 332088, 332089, 332090, 332091, 332092, 332093, 332094, 332095, 332096, 332097, 332098, 332099, 332100, 332101, 332102, 332103, 332104, 332105, 332106, 332107, 332108, 332109, 332110, 332111, 332112, 332113, 332114, 332115, 332116, 332117, 332118, 332119, 332120, 332121, 332122, 332123, 332124, 332125, 332126, 332127, 332128, 332129, 332130, 332131, 332132, 332133, 332134, 332135, 332136, 332137, 332138, 332139, 332140, 332141, 332142, 332143, 332144, 332145, 332146, 332147, 332148, 332149, 332150, 332151, 332152, 332153, 332154, 332155, 332156, 332157, 332158, 332159, 332160, 332161, 332162, 332163, 332164, 332165, 332166, 332167, 332168, 332169, 332170, 332171, 332172, 332173, 332174, 332175, 332176, 332177, 332178, 332179, 332180, 332181, 332182, 332183, 332184, 332185, 332186, 332187, 332188, 332189, 332190, 332191, 332192, 332193, 332194, 332195, 332196, 332197, 332198, 332199, 332200, 332201, 332202, 332203, 332204, 332205, 332206, 332207, 332208, 332209, 332210, 332211, 332212, 332213, 332214, 332215, 332216, 332217, 332218, 332219, 332220, 332221, 332222, 332223, 332224, 332225, 332226, 332227, 332228, 332229, 332230, 332231, 332232, 332233, 332234, 332235, 332236, 332237, 332238, 332239, 332240, 332241, 332242, 332243, 332244, 332245, 332246, 332247, 332248, 332249, 332250, 332251, 332252, 332253, 332254, 332255, 332256, 332257, 332258, 332259, 332260, 332261, 332262, 332263, 332264, 332265, 332266, 332267, 332268, 332269, 332270, 332271, 332272, 332273, 332274, 332275, 332276, 332277, 332278, 332279, 332280, 332281, 332282, 332283, 332284, 332285, 332286, 332287, 332288, 332289, 332290, 332291, 332292, 332293, 332294, 332295, 332296, 332297, 332298, 332299]
2025-07-27 16:35:08.808 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting type-aware smart filtering. Initial total topics: 1616. Required type counts from request: {multiple=40, short=0, judge=20, choice=40, fill=0}
2025-07-27 16:35:08.810 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Initial topic distribution by type (from input list):
2025-07-27 16:35:08.811 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'multiple': 281 topics
2025-07-27 16:35:08.811 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics
2025-07-27 16:35:08.811 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'judge': 387 topics
2025-07-27 16:35:08.811 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'choice': 946 topics
2025-07-27 16:35:08.812 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'hard': 2 topics (present in input, but not in requiredTypeCounts or map is null)
2025-07-27 16:35:08.812 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'short': 0 topics in input (but 0 required)
2025-07-27 16:35:08.812 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter -   - Type 'fill': 0 topics in input (but 0 required)
2025-07-27 16:35:08.812 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'multiple' - required: 40, available in input: 281
2025-07-27 16:35:08.812 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 281 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 16:35:08.813 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 3 knowledge points
2025-07-27 16:35:08.813 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.813 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.813 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 41 topics remaining after reuse interval filtering (out of 62 initial topics).
2025-07-27 16:35:08.814 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.814 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.814 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 74 topics remaining after reuse interval filtering (out of 81 initial topics).
2025-07-27 16:35:08.814 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.814 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.814 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 122 topics remaining after reuse interval filtering (out of 138 initial topics).
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 141 topics remaining (from 281 initial).
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'multiple' contributed 141 topics after smartFilterForType. Total in result now: 141
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'judge' - required: 20, available in input: 387
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 387 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 3 knowledge points
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.815 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.815 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 52 topics remaining after reuse interval filtering (out of 63 initial topics).
2025-07-27 16:35:08.816 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.816 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.816 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 74 topics remaining after reuse interval filtering (out of 104 initial topics).
2025-07-27 16:35:08.816 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.816 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 206 topics remaining after reuse interval filtering (out of 220 initial topics).
2025-07-27 16:35:08.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 150 topics remaining (from 387 initial).
2025-07-27 16:35:08.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'judge' contributed 150 topics after smartFilterForType. Total in result now: 291
2025-07-27 16:35:08.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Processing type 'choice' - required: 40, available in input: 946
2025-07-27 16:35:08.817 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Starting knowledge point level diversity filtering for 946 topics. MinReuseInterval: 1 days, MaxTopicsPerKP: 50
2025-07-27 16:35:08.818 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Topics grouped into 3 knowledge points
2025-07-27 16:35:08.818 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.818 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.818 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 215 topics remaining after reuse interval filtering (out of 236 initial topics).
2025-07-27 16:35:08.819 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.819 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.819 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 213 topics remaining after reuse interval filtering (out of 249 initial topics).
2025-07-27 16:35:08.819 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Applying reuse interval filter. Topics last used before 2025-07-26T16:35:08.819 will be kept. Min reuse interval: 1 days.
2025-07-27 16:35:08.820 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: 396 topics remaining after reuse interval filtering (out of 461 initial topics).
2025-07-27 16:35:08.820 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Knowledge point level filtering completed. 150 topics remaining (from 946 initial).
2025-07-27 16:35:08.820 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type 'choice' contributed 150 topics after smartFilterForType. Total in result now: 441
2025-07-27 16:35:08.820 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DiversityFilter - DiversityFilter: Type-aware filtering completed. Final total topics selected: 441. Initial total topics was: 1616
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 3: After Type-Aware Smart Diversity Filter (with KP-level control), 441 topics available for GA (input size was 1616). MinReuseIntervalDays: null
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Diversity filtering already applied via smartFilter. 441 topics remain.
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4a: Checking for precise allocation requirements...
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Checking knowledge point configurations for short answer requirements...
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 602: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 603: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - KP 269: includeShortAnswer=false, shortAnswerCount=0, hasShortAnswerConfiguration=false
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Overall hasShortAnswerConfiguration: false
2025-07-27 16:35:08.821 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Step 4b: Executing genetic algorithm for basic types with 441 candidate topics...
2025-07-27 16:35:08.822 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA will handle basic types only: {multiple=40, judge=20, choice=40, fill=0}
2025-07-27 16:35:08.822 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - GA target score: 300 (total 300 - short answer 0)
2025-07-27 16:35:08.854 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Created execution record with ID: 14
2025-07-27 16:35:08.863 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA started - Population: 300, Generations: 200, Questions: 441
2025-07-27 16:35:08.946 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Generated 90 greedy seed chromosomes
2025-07-27 16:35:08.956 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized enhanced population with 300 chromosomes
2025-07-27 16:35:08.956 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Intelligent population initialized: 300 total chromosomes
2025-07-27 16:35:09.166 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 1: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4902779283802093ms, NoImprove=0.48555498689545723, Elite=0.5171926633770714
2025-07-27 16:35:09.356 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 2: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49145984557160716ms, NoImprove=0.4867848898112744, Elite=0.48476504736748144
2025-07-27 16:35:09.467 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 16:35:09.478 [http-nio-8081-exec-1] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=44, pages=9, current=1, size=5, records=5
2025-07-27 16:35:09.544 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 3: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4916469222381825ms, NoImprove=0.4875429923582421, Elite=0.49253700999853406
2025-07-27 16:35:09.695 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 4: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4917388732017526ms, NoImprove=0.4880875878758742, Elite=0.47399791379218986
2025-07-27 16:35:09.839 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 5: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4919407507332597ms, NoImprove=0.48859306542178776, Elite=0.47135034839325957
2025-07-27 16:35:10.517 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 10: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4921708848571124ms, NoImprove=0.48997322717464104, Elite=0.3682270497532465
2025-07-27 16:35:11.380 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 20: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49267008788900435ms, NoImprove=0.49120408226185663, Elite=0.2592959630487387
2025-07-27 16:35:12.070 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 30: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.4933760191288003ms, NoImprove=0.4831134406743206, Elite=0.02952319093465794
2025-07-27 16:35:12.783 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 40: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49410363709772515ms, NoImprove=0.48498413587362194, Elite=0.006408560583673547
2025-07-27 16:35:13.484 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 50: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49430598608846316ms, NoImprove=0.4852199543202365, Elite=0.04816899968402583
2025-07-27 16:35:14.155 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Gen 60: Best={:.4f}, Avg={:.4f}, Diversity={:.3f}, Time=0.49430598608846316ms, NoImprove=0.4849097197406334, Elite=0.038240272754516684
2025-07-27 16:35:14.225 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Early termination at generation 60
2025-07-27 16:35:14.225 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Enhanced GA completed in 5362ms. Best fitness: {:.4f}, Selected: 0.49430598608846316 topics
2025-07-27 16:35:14.226 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Algorithm performance metrics: {avg_diversity=0.14309848501743053, convergence_rate=1.3693109816173917E-4, total_time_ms=5362, generations=60, final_diversity=0.038240272754516684}
2025-07-27 16:35:14.249 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined results: 100 basic type topics + 0 short answer topics = 100 total topics
2025-07-27 16:35:14.249 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined algorithm selected 100 topics with type distribution: {multiple=40, judge=20, choice=40}
2025-07-27 16:35:14.250 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Combined result: All type constraints met!
2025-07-27 16:35:14.250 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 正在使用DP动态规划进行最终优化调整，目标分数: 300, 同时保持题型分布
2025-07-27 16:35:14.250 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Pre-DP adjustment type distribution: {multiple=40, judge=20, choice=40}
2025-07-27 16:35:14.251 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 16:35:14.252 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=40, judgment=20, multipleChoice=40}, Target type counts: {singleChoice=40, fillBlank=0, judgment=20, multipleChoice=40, shortAnswer=0}
2025-07-27 16:35:14.252 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 100 topics
2025-07-27 16:35:14.252 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 16:35:14.252 [DP-Adjuster-Thread] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 16:35:14.253 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - After DP adjustment: Type distribution = {multiple=40, judge=20, choice=40}
2025-07-27 16:35:14.253 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP score adjustment complete. Final total score = 300, with type distribution preserved.
2025-07-27 16:35:14.253 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - DP adjustment completed in 3 ms
2025-07-27 16:35:14.253 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 检查各题型题目数量: 实际={multiple=40, judge=20, choice=40}, 目标={SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - 各题型题目数量满足要求，无需调整
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=40, judgment=20, multipleChoice=40}, Target type counts: {singleChoice=40, fillBlank=0, judgment=20, multipleChoice=40, shortAnswer=0}
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Attempting to fill missing topic types using available topic pool of 1616 topics
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 16:35:14.254 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 16:35:14.255 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Final DP adjustment succeeded. totalScore=300, typeCounts={singleChoice=40, judgment=20, multipleChoice=40}.
2025-07-27 16:35:14.255 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - Paper generation process completed. Returning 100 topics.
2025-07-27 16:35:14.255 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Using topics directly from engine. Count: 100. Requested global counts (for warning reference): {SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 16:35:14.256 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 16:35:14.258 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40}
2025-07-27 16:35:14.258 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40, SHORT=0}
2025-07-27 16:35:14.270 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求40/实际40
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求40/实际40
- SHORT: 请求0/实际0
2025-07-27 16:35:14.272 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 40 topics of non-standard type 'SINGLE_CHOICE' (requested 40)
2025-07-27 16:35:14.272 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 16:35:14.272 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 40 topics of non-standard type 'MULTIPLE_CHOICE' (requested 40)
2025-07-27 16:35:14.275 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40}
2025-07-27 16:35:14.275 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Applying DP adjustment to optimize total score (target: 300) after enforcing type counts...
2025-07-27 16:35:14.275 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - DPAdjuster will receive targetTypeCounts (normalized keys): {singleChoice=40, fillBlank=0, judgment=20, multipleChoice=40, shortAnswer=0}
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Adjusting topics with type constraints. Current score: 300, Target score: 300. Number of topics: 100
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current type counts: {singleChoice=40, judgment=20, multipleChoice=40}, Target type counts: {singleChoice=40, fillBlank=0, judgment=20, multipleChoice=40, shortAnswer=0}
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Current score already matches target score.
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.DPAdjuster - DPAdjuster: Both score and type constraints are satisfied. No adjustment needed.
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Re-enforcing exact type counts after DP adjustment to ensure strict adherence to requested counts
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Starting. Requested counts from frontend: {SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SINGLE_CHOICE' → Mapped key='SINGLE_CHOICE'
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='MULTIPLE_CHOICE' → Mapped key='MULTIPLE_CHOICE'
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='JUDGE' → Mapped key='JUDGE'
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='FILL' → Mapped key='FILL'
2025-07-27 16:35:14.276 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Topic type mapping: Original key='SHORT' → Mapped key='SHORT'
2025-07-27 16:35:14.279 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Topic counts from GA (before enforcement): {SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40}
2025-07-27 16:35:14.279 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Remaining counts after mapping: {FILL=0, SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40, SHORT=0}
2025-07-27 16:35:14.291 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - 
题型可用性详细统计：
- FILL: 请求0/实际0
- SINGLE_CHOICE: 请求40/实际40
- JUDGE: 请求20/实际20
- MULTIPLE_CHOICE: 请求40/实际40
- SHORT: 请求0/实际0
2025-07-27 16:35:14.294 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 40 topics of non-standard type 'SINGLE_CHOICE' (requested 40)
2025-07-27 16:35:14.294 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 20 topics of non-standard type 'JUDGE' (requested 20)
2025-07-27 16:35:14.294 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Added 40 topics of non-standard type 'MULTIPLE_CHOICE' (requested 40)
2025-07-27 16:35:14.297 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - EnforceTypeCounts: Finished. Original GA output size: 100. Final result size: 100. Actual counts per type after enforcement: {SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40}
2025-07-27 16:35:14.297 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - After final enforcement: 100 topics, calculated score: 300 (target score: 300)
2025-07-27 16:35:14.300 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Final type counts: {SINGLE_CHOICE=40, JUDGE=20, MULTIPLE_CHOICE=40}, Requested counts: {SINGLE_CHOICE=40, MULTIPLE_CHOICE=40, JUDGE=20, FILL=0, SHORT=0}
2025-07-27 16:35:14.382 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Saved new paper with ID: 310. Title: 信息技术测试
2025-07-27 16:35:14.593 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Updated usage statistics for up to 100 topics.
2025-07-27 16:35:14.597 [http-nio-8081-exec-10] INFO  com.edu.maizi_edu_sys.service.engine.KnowledgePointUsageTracker - KnowledgePointUsageTracker: Updating usage stats for 3 knowledge points
2025-07-27 16:35:15.642 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Fetching papers with pagination: search='null', type=null, sortField='create_time', isAsc=false, page=0, size=5
2025-07-27 16:35:15.649 [http-nio-8081-exec-7] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Query results: total=45, pages=9, current=1, size=5, records=5
2025-07-27 16:35:16.724 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loading 100 topics for paper id: 310
2025-07-27 16:35:16.732 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Found 100 topics in database (from 100 requested IDs) for paper id: 310
2025-07-27 16:35:16.747 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Returning 100 ordered topics for paper id: 310
2025-07-27 16:35:16.749 [http-nio-8081-exec-5] INFO  com.edu.maizi_edu_sys.service.impl.PaperGenerationServiceImpl - Loaded and mapped difficulty distribution from paper: {hard=0.1, medium=0.7, easy=0.2}
2025-07-27 17:35:11.391 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Initialized ForkJoinPool with parallelism: 12
2025-07-27 17:35:11.517 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - Configuration validated. Population: 300, Generations: 50-200, Timeout: 20s
2025-07-27 17:35:11.518 [main] INFO  com.edu.maizi_edu_sys.service.engine.GeneticSolver - GeneticSolver initialization completed successfully
2025-07-27 17:35:11.538 [main] INFO  com.edu.maizi_edu_sys.service.engine.PaperGenerationEngine - PaperGenerationEngine initialized successfully with all components including PreciseTopicAllocator, Validator, and AlgorithmMonitorDataService.
